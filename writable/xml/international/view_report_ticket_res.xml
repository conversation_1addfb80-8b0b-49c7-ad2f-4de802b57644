<TES_AirTicketRetRS>
    <AirTicketRet>
        <FlightSegments>
            <FlightSegment RPH="1" DepartureDateTime="2013-12-28T13:55:00" FlightNumber="931" TicketStatus="OPEN FOR USE" SegmentType="NORMAL" CrsPnrNo="HR4B1J" CrsType="1E" FPC="false" StopType="O" SegmentStatus="OK" PnrNo="MTQ9YB">
                <OperatingAirline Code=""/>
                <DepartureAirport LocationCode="PEK" Terminal="T3"/>
                <ArrivalAirport LocationCode="FRA" Terminal="T1"/>
                <MarketingAirline Code="CA"/>
                <BookingClassAvail ResBookDesigCode="Y"/>
                <Baggage BaggageWeight="20" BaggagePiece="0"/>
            </FlightSegment>
        </FlightSegments>
        <FlightSegments>
            <FlightSegment RPH="2" TicketStatus="VOID" SegmentType="ARRIVAL_UNKNOWN" FPC="false" StopType="O">
                <OperatingAirline/>
                <DepartureAirport LocationCode="FRA" Terminal=""/>
                <ArrivalAirport LocationCode="PEK" Terminal=""/>
                <MarketingAirline/>
                <BookingClassAvail ResBookDesigCode=" "/>
                <Baggage BaggageWeight="0" BaggagePiece="0"/>
            </FlightSegment>
        </FlightSegments>
        <FlightSegments>
            <FlightSegment RPH="3" TicketStatus="VOID" SegmentType="ARRIVAL_UNKNOWN" FPC="false" StopType="O">
                <OperatingAirline/>
                <DepartureAirport LocationCode="PEK" Terminal=""/>
                <ArrivalAirport LocationCode="FRA" Terminal=""/>
                <MarketingAirline/>
                <BookingClassAvail ResBookDesigCode=" "/>
                <Baggage BaggageWeight="0" BaggagePiece="0"/>
            </FlightSegment>
        </FlightSegments>
        <FlightSegments>
            <FlightSegment RPH="4" DepartureDateTime="2013-12-30T19:15:00" FlightNumber="932" TicketStatus="OPEN FOR USE" SegmentType="NORMAL" CrsPnrNo="HR4B1J" CrsType="1E" FPC="false" StopType="O" SegmentStatus="OK" PnrNo="MTQ9YB">
                <OperatingAirline Code=""/>
                <DepartureAirport LocationCode="FRA" Terminal="T1"/>
                <ArrivalAirport LocationCode="PEK" Terminal="T3"/>
                <MarketingAirline Code="CA"/>
                <BookingClassAvail ResBookDesigCode="Y"/>
                <Baggage BaggageWeight="20" BaggagePiece="0"/>
            </FlightSegment>
        </FlightSegments>
        <AirTraveler PassengerTypeCode="CHD">
            <PersonName>
                <Surname>DIAO/SI</Surname>
            </PersonName>
        </AirTraveler>
        <Ticketing>
            <TicketItemInfo TicketNumber="999-4171348017" TotalAmount="33760" Endorsement="NON-END/PENALTY APPLS" Remark="ISSUED BY: AIR CHINAORG/DST: BJS/BJS BSP-I
E/R: NON-END/PENALTY APPLS
TOUR CODE:
PASSENGER: DIAO/SI CHD
EXCH: CONJ TKT:
O FM:1PEK CA 931 Y 28DEC 1355 OK Y 20KOPENFORUSE
T3T1 RL:MTQ9YB /HR4B1J1E
O TO:2FRA VOID VOID VOIDRL:MTQ9YB /HR4B1J1E
O TO:3PEK VOID VOID VOIDRL:MTQ9YB /HR4B1J1E
O TO:4FRA CA 932 Y 30DEC 1915 OK Y 20KOPENFORUSE
T1T3 RL:MTQ9YB /HR4B1J1E
TO: PEK
FC: M 28DEC13BJS CA FRA2695.95/-BJS/-FRA CA BJS2695.95NUC5391.90ENDROE6.1
20290
FARE: CNY33000.00|FOP:CASH
TAX: CNY 90.00CN|OI:
TAX: CNY 56.00DE|
TAX: CNY353.00OY|FOR ALL TAXES: >DETR:TN/999-4171348017,XTOTAL: CNY33760.00|TKTN: 999-4171348017" ExchangeInfo="" OriginalIssue=""
                            IssueAirline="AIR CHINA" ISI="" MoreTax="false" IT="false" ReceiptPrinted="false" ETicketType="BSP_INTERNATIONAL_ETICKET" OrgCity="BJS" DstCity="BJS" Tax="499"><ConjunctiveTicket TicketNumber=""/>
            </TicketItemInfo>
        </Ticketing>
        <AirItineraryPricingInfo>
            <ItinTotalFare>
                <BaseFare Amount="33000" CurrencyCode="CNY"/>
                <Taxes>
                    <Tax TaxCode="CN" Amount="90" CurrencyCode="CNY"/>
                    <Tax TaxCode="DE" Amount="56" CurrencyCode="CNY"/>
                    <Tax TaxCode="OY" Amount="353" CurrencyCode="CNY"/>
                </Taxes>
                <TourCode/>
                <Eqviu EqviuFare="0"/>
            </ItinTotalFare>
            <FareInfos>
                <FareInfo FareType="Y">
                    <FlightSegmentRef RPH="1"/>
                    <Rule>
                        <FareCompute>M 28DEC13BJS CA FRA2695.95/-BJS/-FRACABJS2695.95NUC5391.90END ROE6.120290</FareCompute>
                    </Rule>
                </FareInfo>
                <FareInfo>
                    <FlightSegmentRef RPH="2"/>
                    <Rule>
                        <FareCompute>M 28DEC13BJS CA FRA2695.95/-BJS/-FRACABJS2695.95NUC5391.90END ROE6.120290</FareCompute>
                    </Rule>
                </FareInfo>
                <FareInfo>
                    <FlightSegmentRef RPH="3"/>
                    <Rule>
                        <FareCompute>M 28DEC13BJS CA FRA2695.95/-BJS/-FRACABJS2695.95NUC5391.90END ROE6.120290</FareCompute>
                    </Rule>
                </FareInfo>
                <FareInfo FareType="Y">
                    <FlightSegmentRef RPH="4"/>
                    <Rule>
                        <FareCompute>M 28DEC13BJS CA FRA2695.95/-BJS/-FRACABJS2695.95NUC5391.90END ROE6.120290</FareCompute>
                    </Rule>
                </FareInfo>
            </FareInfos>
        </AirItineraryPricingInfo>
    </AirTicketRet>
</TES_AirTicketRetRS>