<OTA_AirResRetRS EchoToken="String" TimeStamp="String" Version="String" Target="String" SequenceNmbr="String">
    <AirResRet>
        <FlightSegments>
            <FlightSegment FlightNumber="1355" ArrivalDateTime="2011-08-11T20:20:00.0Z" NumberInParty="3" DepartureDateTime="2011-08-11T16:35:00.0Z" SegmentType="NORMAL" CodeshareInd="false" Ticket="eTicket" ScheduleValidEndDate="1967-08-13" IsChanged="false" RPH="3" Status="RR">
                <DepartureAirport LocationCode="PEK"/>
                <ArrivalAirport LocationCode="HAK"/>
                <MarketingAirline CodeContext="String" CompanyShortName="String" Code="CA"/>
                <BookingClassAvail ResBookDesigCode="A">
                    <SubClass></SubClass>
                </BookingClassAvail>
            </FlightSegment>
        </FlightSegments>
        <BookingReferenceID ID="HQ8WEW">
        </BookingReferenceID>
        <AirTraveler RPH="1" PassengerTypeCode="ADT">
            <PersonName>
                <Surname>郭应禄</Surname>
                <NamePNR>郭应禄</NamePNR>
            </PersonName>
            <PassengerTypeQuantity Age="0"/>
        </AirTraveler>
        <AirTraveler RPH="2" PassengerTypeCode="ADT">
            <PersonName>
                <Surname>殷大奎</Surname>
                <NamePNR>殷大奎</NamePNR>
            </PersonName>
            <PassengerTypeQuantity Age="0"/>
        </AirTraveler>
        <Ticketing IsIssued="false" OfficeCode="SHA320" IssuedType="TL" TicketTimeLimit="2011-08-10T18:00:00.0Z" RPH="0" Remark="TL/1800/10AUG/SHA320"></Ticketing>
        <Responsibility PNRno="" CRS="" OfficeCode="SHA666" RPH="17"/>
        <ContactInfo ContactCity="SHA" RPH="0" ContactInfo="SHA/T SHA/T 021-52918766/SHA GUANG FA AIR TICKET SERVICECO.,LTD/ZHOU JIANABCDEFG">
            <TravelerRefNumber RPH="4"/>
        </ContactInfo>
        <SpecialRemark RemarkType="" RPH="16">
            <Text>CA/NYF0G6</Text>
        </SpecialRemark>
        <OtherServiceInformation RPH="15" Code="">
            <Text>1E CAET TN/9992162073572-9992162073573</Text>
        </OtherServiceInformation>
        <SpecialServiceRequest ServiceQuantity="3" SSRCode="FOID" BirthDate="1967-08-13" RPH="7">
            <Text>FOID</Text>
        </SpecialServiceRequest>
    </AirResRet>
</OTA_AirResRetRS>