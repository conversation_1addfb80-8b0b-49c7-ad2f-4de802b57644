<?php
namespace App\Services\Customer;

use App\Models\FileUploadModel;
use App\Services\BaseService;
use App\Helpers\Tools;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Models\CustomerModel;
use App\Models\CustomerIdcardModel;
use App\Models\CustomerAccountModel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Customer extends BaseService
{
    public function __constructor()
    {
    }

    /**
     * @desc 修改会员状态
     * @param $params
     * @return bool
     * @throws \ReflectionException
     *
     * <AUTHOR> 2025-06-23
     */
    public function editStatus($params)
    {
        $customerModel = model('CustomerModel');
        $id = intval($params['id']);
        $status = intval($params['status']);
        $customer = $customerModel->find($id);
        if (empty($customer)) {
            throw new \Exception("非法id");
        }
        if ($status == $customer['status']) {
            throw new \Exception("状态和库中一致，修改失败");
        }
        $result = $customerModel->save([
            'status'  => $status,
            'id'  => $id
        ]);
        if (empty($customerModel->affectedRows())) {
            throw new \Exception("修改失败");
        }
        return $result;
    }
}