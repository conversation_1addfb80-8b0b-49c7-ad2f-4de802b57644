<?php

namespace App\Services\PnrData;

use App\Services\BaseService;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookOrderPassengerModel;
use App\Models\TicketBookOrderSegmentModel;
use App\Models\TicketBookOrderDetailModel;
use App\Models\TicketBookOrderTicketPriceModel;

/**
 * PNR数据拉取服务
 * 
 * 从IBE接口拉取数据并存储出票订单到数据库
 */
class PullPnrDataService extends BaseService
{
    /**
     * 错误信息收集
     */
    private array $errors = [];
    
    /**
     * 统计信息
     */
    private array $stats = [
        'total_records' => 0,
        'ticket_orders' => 0,
        'skipped_records' => 0
    ];

    /**
     * 拉取并保存数据
     *
     * @param string $date 日期 YYYY-MM-DD
     * @param string $office OFFICE号
     * @return array 执行结果
     */
    public function pullAndSaveData(string $date, string $office): array
    {
        try {
            // 1. 调用IBE接口获取数据
            $reportData = $this->callIbeViewReportApi($date, $office);
            
            if (empty($reportData['tslDetails'])) {
                return [
                    'total_records' => 0,
                    'ticket_orders' => 0,
                    'skipped_records' => 0,
                    'errors' => ['没有获取到数据']
                ];
            }
            
            $this->stats['total_records'] = count($reportData['tslDetails']);
            
            // 2. 过滤出票订单
            $ticketOrders = $this->filterTicketOrders($reportData['tslDetails']);
            
            // 3. 保存订单数据
            foreach ($ticketOrders as $orderData) {
                try {
                    $this->saveTicketOrder($orderData);
                    $this->stats['ticket_orders']++;
                } catch (\Exception $e) {
                    $this->errors[] = "保存订单失败 PNR:{$orderData['pnr']} - " . $e->getMessage();
                    $this->stats['skipped_records']++;
                }
            }
            
            return [
                'total_records' => $this->stats['total_records'],
                'ticket_orders' => $this->stats['ticket_orders'],
                'skipped_records' => $this->stats['skipped_records'],
                'errors' => $this->errors
            ];
            
        } catch (\Exception $e) {
            throw new \Exception("拉取数据失败: " . $e->getMessage());
        }
    }
    
    /**
     * 调用IBE View Report接口
     *
     * @param string $date
     * @param string $office
     * @return array
     */
    private function callIbeViewReportApi(string $date, string $office): array
    {
        // 构建请求参数
        $requestParams = $this->buildRequestParams($date, $office);
        
        // 这里使用模拟数据，实际项目中应该调用真实的IBE接口
        // TODO: 实现真实的HTTP请求调用
        
        // 读取模拟响应数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_res.json';
        if (!file_exists($responseFile)) {
            throw new \Exception("模拟数据文件不存在: {$responseFile}");
        }
        
        $responseData = file_get_contents($responseFile);
        $data = json_decode($responseData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("解析响应数据失败: " . json_last_error_msg());
        }
        
        return $data;
    }
    
    /**
     * 构建请求参数
     *
     * @param string $date
     * @param string $office
     * @return array
     */
    private function buildRequestParams(string $date, string $office): array
    {
        $dateObj = new \DateTime($date);
        
        return [
            'office' => $office,
            'ticketingDate' => [
                'day' => $dateObj->format('d'),
                'month' => $dateObj->format('m'),
                'year' => $dateObj->format('Y')
            ],
            'salesDateTimeStart' => [
                'hour' => '00',
                'minutes' => '00',
                'seconds' => '00'
            ],
            'salesDateTimeEnd' => [
                'hour' => '23',
                'minutes' => '59',
                'seconds' => '59'
            ]
        ];
    }
    
    /**
     * 过滤出票订单
     *
     * @param array $tslDetails
     * @return array
     */
    private function filterTicketOrders(array $tslDetails): array
    {
        $ticketOrders = [];
        
        foreach ($tslDetails as $detail) {
            // 只处理出票订单，过滤退款、改签等订单
            if ($detail['saleStatusCode'] === 'ISSU') {
                $ticketOrders[] = $detail;
            } else {
                $this->stats['skipped_records']++;
            }
        }
        
        return $ticketOrders;
    }
    
    /**
     * 保存出票订单
     *
     * @param array $orderData
     * @return void
     */
    private function saveTicketOrder(array $orderData): void
    {
        try {
            // 检查订单是否已存在（通过PNR检查）
            $orderModel = new TicketBookOrderModel();
            $existingOrder = $orderModel->where('pnr', $orderData['pnr'])->first();

            if ($existingOrder) {
                return; // 订单已存在，跳过
            }

            // 1. 保存主订单
            $orderId = $this->saveMainOrder($orderData);
            if (!$orderId) {
                throw new \Exception("保存主订单失败");
            }

            // 暂时只保存主订单，其他表后续完善
            // TODO: 完善其他表的保存逻辑

        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 保存主订单
     *
     * @param array $orderData
     * @return int 订单ID
     */
    private function saveMainOrder(array $orderData): int
    {
        $orderModel = new TicketBookOrderModel();
        
        // 生成订单号
        $orderNo = $orderModel->generate_order_no('T');
        
        // 解析航班日期
        $flightDate = $this->parseFlightDate($orderData['beginFlightDate']);
        
        $orderInfo = [
            'order_no' => $orderNo,
            'pnr' => $orderData['pnr'],
            'journey_type' => 1, // 单程
            'passenger_number' => 1,
            'total_supplier_amount' => $orderData['totalAmount'],
            'total_customer_amount' => $orderData['totalAmount'],
            'status' => 2 // 已出票
        ];
        
        return $orderModel->insert($orderInfo);
    }

    /**
     * 保存乘客信息
     *
     * @param int $orderId
     * @param array $orderData
     * @return int 乘客ID
     */
    private function saveOrderPassenger(int $orderId, array $orderData): int
    {
        $passengerModel = new TicketBookOrderPassengerModel();

        // 从票号中提取乘客信息（IBE数据中没有具体的乘客姓名）
        $passengerInfo = [
            'order_id' => $orderId,
            'rph' => 1, // 乘客序号
            'person_name' => 'IBE乘客', // IBE数据中无乘客姓名，使用默认值
            'passenger_type' => $this->mapPassengerType($orderData['passengerType']),
            'ticket_number' => $orderData['ticketNumber']
        ];

        return $passengerModel->insert($passengerInfo);
    }

    /**
     * 保存航段信息
     *
     * @param int $orderId
     * @param array $orderData
     * @return void
     */
    private function saveOrderSegment(int $orderId, array $orderData): void
    {
        $segmentModel = new TicketBookOrderSegmentModel();

        // 解析航班日期和时间
        $flightDate = $this->parseFlightDate($orderData['beginFlightDate']);
        $departureDateTime = $flightDate . ' 00:00:00'; // IBE数据中没有具体时间
        $arrivalDateTime = $flightDate . ' 23:59:59';   // 默认值

        $segmentInfo = [
            'order_id' => $orderId,
            'rph' => 1, // 航段序号
            'departure_datetime' => $departureDateTime,
            'arrival_datetime' => $arrivalDateTime,
            'airline' => $orderData['airline'],
            'flight_number' => $orderData['flightNo'] . ($orderData['flightSuffix'] ?? ''),
            'operating_airline' => $orderData['airline'],
            'operating_flight_number' => $orderData['flightNo'] . ($orderData['flightSuffix'] ?? ''),
            'passenger_number' => 1,
            'action_code' => 'HK' // 默认确认状态
        ];

        $segmentModel->insert($segmentInfo);
    }

    /**
     * 保存订单详情
     *
     * @param int $orderId
     * @param int $passengerId
     * @param array $orderData
     * @return void
     */
    private function saveOrderDetail(int $orderId, int $passengerId, array $orderData): void
    {
        $detailModel = new TicketBookOrderDetailModel();

        $detailInfo = [
            'order_id' => $orderId,
            'order_passenger_id' => $passengerId,
            'ticket_total_price' => $orderData['totalAmount']
        ];

        $detailModel->insert($detailInfo);
    }

    /**
     * 保存票价信息
     *
     * @param int $orderId
     * @param int $passengerId
     * @param array $orderData
     * @return void
     */
    private function saveOrderTicketPrice(int $orderId, int $passengerId, array $orderData): void
    {
        $priceModel = new TicketBookOrderTicketPriceModel();

        $priceInfo = [
            'order_id' => $orderId,
            'order_passenger_id' => $passengerId,
            'passenger_type' => $this->mapPassengerType($orderData['passengerType']),
            'total_amount' => $orderData['totalAmount']
        ];

        $priceModel->insert($priceInfo);
    }

    /**
     * 映射乘客类型
     *
     * @param string $passengerType IBE乘客类型
     * @return int 系统乘客类型
     */
    private function mapPassengerType($passengerType): int
    {
        // IBE乘客类型映射到系统类型
        // D = 成人, C = 儿童, I = 婴儿
        if (empty($passengerType)) {
            return 1; // 默认成人
        }

        switch (strtoupper($passengerType)) {
            case 'D':
            case 'ADT':
                return 1; // 成人
            case 'C':
            case 'CNN':
                return 2; // 儿童
            case 'I':
            case 'INF':
                return 3; // 婴儿
            default:
                return 1; // 默认成人
        }
    }

    /**
     * 解析航班日期
     *
     * @param string $dateString 格式：20221005
     * @return string 格式：2022-10-05
     */
    private function parseFlightDate(string $dateString): string
    {
        if (strlen($dateString) !== 8) {
            throw new \Exception("航班日期格式错误: {$dateString}");
        }

        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);

        return "{$year}-{$month}-{$day}";
    }
}
