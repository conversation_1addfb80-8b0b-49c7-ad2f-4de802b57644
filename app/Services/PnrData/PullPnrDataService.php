<?php

namespace App\Services\PnrData;

use App\Models\OrderModel;
use App\Services\BaseService;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookOrderPassengerModel;
use App\Models\TicketBookOrderSegmentModel;
use App\Models\TicketBookOrderDetailModel;
use App\Models\TicketBookOrderTicketPriceModel;
use App\Libraries\Api\IBE\Ticket;

/**
 * PNR数据拉取服务
 *
 * 从IBE接口拉取数据并存储出票订单到数据库
 */
class PullPnrDataService extends BaseService
{
    /**
     * IBE Ticket API实例
     */
    private Ticket $ibeTicket;

    /**
     * 错误信息收集
     */
    private array $errors = [];

    /**
     * 统计信息
     */
    private array $stats = [
        'total_records'   => 0,
        'ticket_orders'   => 0,
        'skipped_records' => 0,
    ];

    public function __construct()
    {
        $this->ibeTicket = new Ticket();
    }

    /**
     * 拉取并保存数据
     *
     * @param  string  $date  日期 YYYY-MM-DD
     * @param  string  $start  时间 12:00:30
     * @param  string  $end  时间 23:59:59
     *
     * @return array 执行结果
     * @throws \Exception
     */
    public function pullAndSaveData(string $date, string $start, string $end): array
    {
        try {
            // 1. 调用IBE接口获取数据
            $reportData = $this->ibeTicket->viewReport($date, $start, $end);

            if (empty($reportData['tslDetails'])) {
                return [
                    'total_records'   => 0,
                    'ticket_orders'   => 0,
                    'skipped_records' => 0,
                    'errors'          => ['没有获取到数据'],
                ];
            }

            $this->stats['total_records'] = count($reportData['tslDetails']);

            // 2. 过滤出票订单
            $ticketOrders = $this->ibeTicket->filterTicketOrders($reportData['tslDetails']);

            // 3. 处理每个出票订单，获取详细信息并保存
            foreach ($ticketOrders as $orderData) {
                try {
                    // 3.1 根据PNR获取详细信息
                    $pnrDetail = $this->ibeTicket->viewReportPnr($orderData['pnr']);

                    // 3.2 根据票号获取票务信息
                    $ticketDetail = $this->ibeTicket->viewReportTicket($orderData['ticketNumber'], $orderData['pnr']);

                    // 3.3 整合数据并保存
                    $completeOrderData = $this->mergeOrderData($orderData, $pnrDetail, $ticketDetail);
                    $this->saveCompleteTicketOrder($completeOrderData);

                    $this->stats['ticket_orders']++;
                } catch (\Exception $e) {
                    $this->errors[] = "保存订单失败 PNR:{$orderData['pnr']} - " . $e->getMessage();
                    $this->stats['skipped_records']++;
                }
            }

            return [
                'total_records'   => $this->stats['total_records'],
                'ticket_orders'   => $this->stats['ticket_orders'],
                'skipped_records' => $this->stats['skipped_records'],
                'errors'          => $this->errors,
            ];

        } catch (\Exception $e) {
            throw new \Exception("拉取数据失败: " . $e->getMessage());
        }
    }

    /**
     * 整合订单数据
     *
     * @param  array  $baseOrderData  基础订单数据
     * @param  array  $pnrDetail  PNR详细信息
     * @param  array  $ticketDetail  票务详细信息
     *
     * @return array
     */
    private function mergeOrderData(array $baseOrderData, array $pnrDetail, array $ticketDetail): array
    {
        return [
            'base_order'    => $baseOrderData,
            'pnr_detail'    => $pnrDetail,
            'ticket_detail' => $ticketDetail,
        ];
    }

    /**
     * 保存完整的出票订单
     *
     * @param  array  $completeOrderData
     *
     * @return void
     */
    private function saveCompleteTicketOrder(array $completeOrderData): void
    {
        try {
            $baseOrder    = $completeOrderData['base_order'];
            $pnrDetail    = $completeOrderData['pnr_detail'];
            $ticketDetail = $completeOrderData['ticket_detail'];

            // 检查订单是否已存在（通过PNR检查）
            $orderModel    = new TicketBookOrderModel();
            $existingOrder = $orderModel->where('pnr', $baseOrder['pnr'])->first();
            if ($existingOrder) {
                return; // 订单已存在，跳过
            }

            // 1. 保存主订单
            $orderId = $this->saveCompleteMainOrder($baseOrder, $pnrDetail);
            if (!$orderId) {
                throw new \Exception("保存主订单失败");
            }

            // 2. 保存乘客信息
            $passengerIds = $this->saveCompleteOrderPassengers($orderId, $pnrDetail, $ticketDetail);

            // 3. 保存航段信息
            $this->saveCompleteOrderSegments($orderId, $pnrDetail, $ticketDetail);

            // 4. 保存订单详情和票价信息
            $this->saveCompleteOrderDetails($orderId, $passengerIds, $ticketDetail);

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 保存出票订单
     *
     * @param  array  $orderData
     *
     * @return void
     * @throws \Exception
     */
    private function saveTicketOrder(array $orderData): void
    {
        try {
            // 检查订单是否已存在（通过PNR检查）
            $orderModel    = new TicketBookOrderModel();
            $existingOrder = $orderModel->where('pnr', $orderData['pnr'])->first();

            if ($existingOrder) {
                return; // 订单已存在，跳过
            }

            // 1. 保存主订单
            $orderId = $this->saveMainOrder($orderData);
            if (!$orderId) {
                throw new \Exception("保存主订单失败");
            }

            // 暂时只保存主订单，其他表后续完善
            // TODO: 完善其他表的保存逻辑

        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * 保存主订单
     *
     * @param  array  $orderData
     *
     * @return int 订单ID
     * @throws \ReflectionException
     */
    private function saveMainOrder(array $orderData): int
    {
        $orderModel = new TicketBookOrderModel();

        // 生成订单号
        $orderNo = $orderModel->generate_order_no('T');

        // 解析航班日期
        $flightDate = $this->parseFlightDate($orderData['beginFlightDate']);

        $orderInfo = [
            'order_no'              => $orderNo,
            'pnr'                   => $orderData['pnr'],
            'journey_type'          => 1, // 单程
            'passenger_number'      => 1,
            'total_supplier_amount' => $orderData['totalAmount'],
            'total_customer_amount' => $orderData['totalAmount'],
            'status'                => 2, // 已出票
        ];

        return $orderModel->insert($orderInfo);
    }

    /**
     * 解析航班日期
     *
     * @param  string  $dateString  格式：20221005
     *
     * @return string 格式：2022-10-05
     * @throws \Exception
     */
    private function parseFlightDate(string $dateString): string
    {
        if (strlen($dateString) !== 8) {
            throw new \Exception("航班日期格式错误: {$dateString}");
        }

        $year  = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day   = substr($dateString, 6, 2);

        return "{$year}-{$month}-{$day}";
    }

    /**
     * 保存完整的主订单
     *
     * @param  array  $baseOrder
     * @param  array  $pnrDetail
     *
     * @return int
     * @throws \ReflectionException
     * @throws \Exception
     */
    private function saveCompleteMainOrder(array $baseOrder, array $pnrDetail): int
    {
        $orderModel = new TicketBookOrderModel();

        // 生成订单号
        $orderNo = $orderModel->generate_order_no('T');

        // 解析航班日期
        $flightDate = $this->parseFlightDate($baseOrder['beginFlightDate']);

        // 计算乘客数量和航程类型
        $passengerNumber = count($pnrDetail['passengers']);
        $journeyType     = $this->determineJourneyType($pnrDetail['segments']);

        // 构建航程信息
        $journeyInfo = $this->buildJourneyInfo($pnrDetail['segments']);

        // 获取乘客姓名
        $passengerNames = $this->buildPassengerNames($pnrDetail['passengers']);

        $orderInfo = [
            'order_no'              => $orderNo,
            'order_type'            => 1,
            'pnr'                   => $baseOrder['pnr'],
            'journey_type'          => $journeyType,
            'journey_info'          => $journeyInfo,
            'passenger_number'      => $passengerNumber,
            'passenger_names'       => $passengerNames,
            'total_supplier_amount' => $baseOrder['totalAmount'],
            'total_customer_amount' => $baseOrder['totalAmount'],
            'status'                => 2, // 已出票
            'area_type'             => TicketBookOrderModel::ORDER_AREA_INTL, // 国际
            'order_source'          => 5, // B2B订单
            'customer_type'         => 1, // 自有客户
            'contact_name'          => $this->extractContactName($pnrDetail['contact_info']),
            'contact_telephone'     => $this->extractContactPhone($pnrDetail['contact_info']),
            'office'                => $pnrDetail['ticketing_info']['office_code'] ?? config('IBE')->office ?? 'BJS191',
        ];

        return $orderModel->insert($orderInfo);
    }

    /**
     * 保存完整的乘客信息
     *
     * @param  int  $orderId
     * @param  array  $pnrDetail
     * @param  array  $ticketDetail
     *
     * @return array 乘客ID数组
     * @throws \ReflectionException
     */
    private function saveCompleteOrderPassengers(int $orderId, array $pnrDetail, array $ticketDetail): array
    {
        $passengerModel = new TicketBookOrderPassengerModel();
        $passengerIds   = [];

        foreach ($pnrDetail['passengers'] as $passenger) {
            $passengerInfo = [
                'order_id'       => $orderId,
                'rph'            => $passenger['rph'],
                'person_name'    => $passenger['surname'],
                'passenger_type' => $this->ibeTicket->mapPassengerType($passenger['passenger_type']),
                'ticket_number'  => $ticketDetail['ticketing']['ticket_number'] ?? '',
            ];

            $passengerId    = $passengerModel->insert($passengerInfo);
            $passengerIds[] = $passengerId;
        }

        return $passengerIds;
    }

    /**
     * 保存完整的航段信息
     *
     * @param  int  $orderId
     * @param  array  $pnrDetail
     * @param  array  $ticketDetail
     *
     * @return void
     * @throws \ReflectionException
     */
    private function saveCompleteOrderSegments(int $orderId, array $pnrDetail, array $ticketDetail): void
    {
        $segmentModel = new TicketBookOrderSegmentModel();

        if (empty($pnrDetail['segments'])) {
            throw new \Exception("PNR详情中没有航段信息");
        }

        foreach ($pnrDetail['segments'] as $segment) {
            $segmentInfo = [
                'order_id'                => $orderId,
                'rph'                     => $segment['rph'],
                'departure_datetime'      => $this->formatDateTime($segment['departure_datetime']),
                'arrival_datetime'        => $this->formatDateTime($segment['arrival_datetime']),
                'airline'                 => $segment['marketing_airline'],
                'flight_number'           => $segment['flight_number'],
                'operating_airline'       => $segment['marketing_airline'],
                'operating_flight_number' => $segment['flight_number'],
                'cabin'                   => $segment['booking_class'],
                'passenger_number'        => $segment['passenger_number'],
                'action_code'             => $segment['status'],
            ];

            $result = $segmentModel->insert($segmentInfo);
            if (!$result) {
                throw new \Exception("保存航段信息失败: " . json_encode($segmentInfo));
            }
        }
    }

    /**
     * 保存完整的订单详情和票价信息
     *
     * @param  int  $orderId
     * @param  array  $passengerIds
     * @param  array  $ticketDetail
     *
     * @return void
     * @throws \ReflectionException
     */
    private function saveCompleteOrderDetails(int $orderId, array $passengerIds, array $ticketDetail): void
    {
        $detailModel = new TicketBookOrderDetailModel();

        foreach ($passengerIds as $passengerId) {
            // 保存订单详情
            $detailInfo = [
                'order_id'           => $orderId,
                'order_passenger_id' => $passengerId,
                'ticket_total_price' => $ticketDetail['ticketing']['total_amount'] ?? 0,
            ];
            $detailModel->insert($detailInfo);

            // 票价信息表不存在，暂时跳过
            // TODO: 如果需要保存票价信息，需要先创建对应的数据库表
        }
    }

    /**
     * 确定航程类型
     *
     * @param  array  $segments
     *
     * @return int
     */
    private function determineJourneyType(array $segments): int
    {
        $segmentCount = count($segments);

        if ($segmentCount == 1) {
            return 1; // 单程
        } elseif ($segmentCount == 2) {
            // 检查是否为往返
            $firstSegment = $segments[0];
            $lastSegment  = $segments[$segmentCount - 1];

            if ($firstSegment['departure_airport'] == $lastSegment['arrival_airport'] &&
                $firstSegment['arrival_airport'] == $lastSegment['departure_airport']) {
                return 2; // 往返
            } else {
                return 3; // 联程-两航段
            }
        } elseif ($segmentCount <= 4) {
            return 4; // 联程-多航段
        } else {
            return 5; // 多航段
        }
    }

    /**
     * 构建航程信息
     *
     * @param  array  $segments
     *
     * @return string
     */
    private function buildJourneyInfo(array $segments): string
    {
        if (empty($segments)) {
            return '';
        }

        $airports   = [];
        $airports[] = $segments[0]['departure_airport'];

        foreach ($segments as $segment) {
            $airports[] = $segment['arrival_airport'];
        }

        return implode('', $airports);
    }

    /**
     * 构建乘客姓名字符串
     *
     * @param  array  $passengers
     *
     * @return string
     */
    private function buildPassengerNames(array $passengers): string
    {
        $names = [];
        foreach ($passengers as $passenger) {
            $names[] = $passenger['surname'];
        }

        return implode(',', $names);
    }

    /**
     * 提取联系人姓名
     *
     * @param  string  $contactInfo
     *
     * @return string
     */
    private function extractContactName(string $contactInfo): string
    {
        // 从联系信息中提取姓名，这里简化处理
        if (preg_match('/\/([A-Z\s]+)\//', $contactInfo, $matches)) {
            return trim($matches[1]);
        }

        return '';
    }

    /**
     * 提取联系电话
     *
     * @param  string  $contactInfo
     *
     * @return string
     */
    private function extractContactPhone(string $contactInfo): string
    {
        // 从联系信息中提取电话，这里简化处理
        if (preg_match('/(\d{3,4}-\d{7,8}|\d{11})/', $contactInfo, $matches)) {
            return $matches[1];
        }

        return '';
    }

    /**
     * 格式化日期时间
     *
     * @param  string  $dateTime
     *
     * @return string
     */
    private function formatDateTime(string $dateTime): string
    {
        try {
            $dt = new \DateTime($dateTime);

            return $dt->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            // 如果解析失败，返回默认值
            return date('Y-m-d H:i:s');
        }
    }
}
