<?php

namespace App\Services\PnrData;

use App\Services\BaseService;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookOrderPassengerModel;
use App\Models\TicketBookOrderSegmentModel;
use App\Models\TicketBookOrderDetailModel;
use App\Models\TicketBookOrderTicketPriceModel;

/**
 * PNR数据拉取服务
 * 
 * 从IBE接口拉取数据并存储出票订单到数据库
 */
class PullPnrDataService extends BaseService
{
    /**
     * 错误信息收集
     */
    private array $errors = [];
    
    /**
     * 统计信息
     */
    private array $stats = [
        'total_records' => 0,
        'ticket_orders' => 0,
        'skipped_records' => 0
    ];

    /**
     * 拉取并保存数据
     *
     * @param  string  $date  日期 YYYY-MM-DD
     * @param  string  $office  OFFICE号
     *
     * @return array 执行结果
     * @throws \Exception
     */
    public function pullAndSaveData(string $date, string $office): array
    {
        try {
            // 1. 调用IBE接口获取数据
            $reportData = $this->callIbeViewReportApi($date, $office);
            
            if (empty($reportData['tslDetails'])) {
                return [
                    'total_records' => 0,
                    'ticket_orders' => 0,
                    'skipped_records' => 0,
                    'errors' => ['没有获取到数据']
                ];
            }
            
            $this->stats['total_records'] = count($reportData['tslDetails']);
            
            // 2. 过滤出票订单
            $ticketOrders = $this->filterTicketOrders($reportData['tslDetails']);
            
            // 3. 处理每个出票订单，获取详细信息并保存
            foreach ($ticketOrders as $orderData) {
                try {
                    // 3.1 根据PNR获取详细信息
                    $pnrDetail = $this->callIbePnrDetailApi($orderData['pnr'], $office);

                    // 3.2 根据票号获取票务信息
                    $ticketDetail = $this->callIbeTicketDetailApi($orderData['ticketNumber'], $office, $orderData['pnr']);

                    // 3.3 整合数据并保存
                    $completeOrderData = $this->mergeOrderData($orderData, $pnrDetail, $ticketDetail);
                    $this->saveCompleteTicketOrder($completeOrderData);

                    $this->stats['ticket_orders']++;
                } catch (\Exception $e) {
                    $this->errors[] = "保存订单失败 PNR:{$orderData['pnr']} - " . $e->getMessage();
                    $this->stats['skipped_records']++;
                }
            }
            
            return [
                'total_records' => $this->stats['total_records'],
                'ticket_orders' => $this->stats['ticket_orders'],
                'skipped_records' => $this->stats['skipped_records'],
                'errors' => $this->errors
            ];
            
        } catch (\Exception $e) {
            throw new \Exception("拉取数据失败: " . $e->getMessage());
        }
    }

    /**
     * 调用IBE View Report接口
     *
     * @param  string  $date
     * @param  string  $office
     *
     * @return array
     * @throws \Exception
     */
    private function callIbeViewReportApi(string $date, string $office): array
    {
        // 构建请求参数
        $requestParams = $this->buildRequestParams($date, $office);
        
        // 这里使用模拟数据，实际项目中应该调用真实的IBE接口
        // TODO: 实现真实的HTTP请求调用
        
        // 读取模拟响应数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_res.json';
        if (!file_exists($responseFile)) {
            throw new \Exception("模拟数据文件不存在: {$responseFile}");
        }
        
        $responseData = file_get_contents($responseFile);
        $data = json_decode($responseData, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new \Exception("解析响应数据失败: " . json_last_error_msg());
        }
        
        return $data;
    }

    /**
     * 调用IBE PNR结构化信息提取接口
     *
     * @param string $pnr
     * @param string $office
     * @return array
     */
    private function callIbePnrDetailApi(string $pnr, string $office): array
    {
        // 构建PNR查询请求XML
        $requestXml = $this->buildPnrDetailRequestXml($pnr, $office);

        // 这里使用模拟数据，实际项目中应该调用真实的IBE接口
        // TODO: 实现真实的HTTP请求调用

        // 读取模拟响应数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_pnr_res.xml';
        if (!file_exists($responseFile)) {
            throw new \Exception("PNR模拟数据文件不存在: {$responseFile}");
        }

        $responseData = file_get_contents($responseFile);
        return $this->parsePnrDetailResponse($responseData);
    }

    /**
     * 调用IBE电子客票信息提取接口
     *
     * @param string $ticketNumber
     * @param string $office
     * @param string $pnr
     * @return array
     */
    private function callIbeTicketDetailApi(string $ticketNumber, string $office, string $pnr): array
    {
        // 构建票务查询请求XML
        $requestXml = $this->buildTicketDetailRequestXml($ticketNumber, $office, $pnr);

        // 这里使用模拟数据，实际项目中应该调用真实的IBE接口
        // TODO: 实现真实的HTTP请求调用

        // 读取模拟响应数据
        $responseFile = ROOTPATH . 'writable/xml/international/view_report_ticket_res.xml';
        if (!file_exists($responseFile)) {
            throw new \Exception("票务模拟数据文件不存在: {$responseFile}");
        }

        $responseData = file_get_contents($responseFile);
        return $this->parseTicketDetailResponse($responseData);
    }
    
    /**
     * 构建请求参数
     *
     * @param string $date
     * @param string $office
     * @return array
     */
    private function buildRequestParams(string $date, string $office): array
    {
        $dateObj = new \DateTime($date);
        
        return [
            'office' => $office,
            'ticketingDate' => [
                'day' => $dateObj->format('d'),
                'month' => $dateObj->format('m'),
                'year' => $dateObj->format('Y')
            ],
            'salesDateTimeStart' => [
                'hour' => '00',
                'minutes' => '00',
                'seconds' => '00'
            ],
            'salesDateTimeEnd' => [
                'hour' => '23',
                'minutes' => '59',
                'seconds' => '59'
            ]
        ];
    }
    
    /**
     * 过滤出票订单
     *
     * @param array $tslDetails
     * @return array
     */
    private function filterTicketOrders(array $tslDetails): array
    {
        $ticketOrders = [];
        
        foreach ($tslDetails as $detail) {
            // 只处理出票订单，过滤退款、改签等订单
            if ($detail['saleStatusCode'] === 'ISSU') {
                $ticketOrders[] = $detail;
            } else {
                $this->stats['skipped_records']++;
            }
        }
        
        return $ticketOrders;
    }

    /**
     * 整合订单数据
     *
     * @param array $baseOrderData 基础订单数据
     * @param array $pnrDetail PNR详细信息
     * @param array $ticketDetail 票务详细信息
     * @return array
     */
    private function mergeOrderData(array $baseOrderData, array $pnrDetail, array $ticketDetail): array
    {
        return [
            'base_order' => $baseOrderData,
            'pnr_detail' => $pnrDetail,
            'ticket_detail' => $ticketDetail
        ];
    }

    /**
     * 保存完整的出票订单
     *
     * @param array $completeOrderData
     * @return void
     */
    private function saveCompleteTicketOrder(array $completeOrderData): void
    {
        try {
            $baseOrder = $completeOrderData['base_order'];
            $pnrDetail = $completeOrderData['pnr_detail'];
            $ticketDetail = $completeOrderData['ticket_detail'];

            // 检查订单是否已存在（通过PNR检查）
            $orderModel = new TicketBookOrderModel();
            $existingOrder = $orderModel->where('pnr', $baseOrder['pnr'])->first();

            if ($existingOrder) {
                return; // 订单已存在，跳过
            }

            // 1. 保存主订单
            $orderId = $this->saveCompleteMainOrder($baseOrder, $pnrDetail, $ticketDetail);
            if (!$orderId) {
                throw new \Exception("保存主订单失败");
            }

            // 2. 保存乘客信息
            $passengerIds = $this->saveCompleteOrderPassengers($orderId, $pnrDetail, $ticketDetail);

            // 3. 保存航段信息
            $this->saveCompleteOrderSegments($orderId, $pnrDetail, $ticketDetail);

            // 4. 保存订单详情和票价信息
            $this->saveCompleteOrderDetails($orderId, $passengerIds, $ticketDetail);

        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 保存出票订单
     *
     * @param array $orderData
     * @return void
     */
    private function saveTicketOrder(array $orderData): void
    {
        try {
            // 检查订单是否已存在（通过PNR检查）
            $orderModel = new TicketBookOrderModel();
            $existingOrder = $orderModel->where('pnr', $orderData['pnr'])->first();

            if ($existingOrder) {
                return; // 订单已存在，跳过
            }

            // 1. 保存主订单
            $orderId = $this->saveMainOrder($orderData);
            if (!$orderId) {
                throw new \Exception("保存主订单失败");
            }

            // 暂时只保存主订单，其他表后续完善
            // TODO: 完善其他表的保存逻辑

        } catch (\Exception $e) {
            throw $e;
        }
    }
    
    /**
     * 保存主订单
     *
     * @param array $orderData
     * @return int 订单ID
     */
    private function saveMainOrder(array $orderData): int
    {
        $orderModel = new TicketBookOrderModel();
        
        // 生成订单号
        $orderNo = $orderModel->generate_order_no('T');
        
        // 解析航班日期
        $flightDate = $this->parseFlightDate($orderData['beginFlightDate']);
        
        $orderInfo = [
            'order_no' => $orderNo,
            'pnr' => $orderData['pnr'],
            'journey_type' => 1, // 单程
            'passenger_number' => 1,
            'total_supplier_amount' => $orderData['totalAmount'],
            'total_customer_amount' => $orderData['totalAmount'],
            'status' => 2 // 已出票
        ];
        
        return $orderModel->insert($orderInfo);
    }

    /**
     * 保存乘客信息
     *
     * @param int $orderId
     * @param array $orderData
     * @return int 乘客ID
     */
    private function saveOrderPassenger(int $orderId, array $orderData): int
    {
        $passengerModel = new TicketBookOrderPassengerModel();

        // 从票号中提取乘客信息（IBE数据中没有具体的乘客姓名）
        $passengerInfo = [
            'order_id' => $orderId,
            'rph' => 1, // 乘客序号
            'person_name' => 'IBE乘客', // IBE数据中无乘客姓名，使用默认值
            'passenger_type' => $this->mapPassengerType($orderData['passengerType']),
            'ticket_number' => $orderData['ticketNumber']
        ];

        return $passengerModel->insert($passengerInfo);
    }

    /**
     * 保存航段信息
     *
     * @param int $orderId
     * @param array $orderData
     * @return void
     */
    private function saveOrderSegment(int $orderId, array $orderData): void
    {
        $segmentModel = new TicketBookOrderSegmentModel();

        // 解析航班日期和时间
        $flightDate = $this->parseFlightDate($orderData['beginFlightDate']);
        $departureDateTime = $flightDate . ' 00:00:00'; // IBE数据中没有具体时间
        $arrivalDateTime = $flightDate . ' 23:59:59';   // 默认值

        $segmentInfo = [
            'order_id' => $orderId,
            'rph' => 1, // 航段序号
            'departure_datetime' => $departureDateTime,
            'arrival_datetime' => $arrivalDateTime,
            'airline' => $orderData['airline'],
            'flight_number' => $orderData['flightNo'] . ($orderData['flightSuffix'] ?? ''),
            'operating_airline' => $orderData['airline'],
            'operating_flight_number' => $orderData['flightNo'] . ($orderData['flightSuffix'] ?? ''),
            'passenger_number' => 1,
            'action_code' => 'HK' // 默认确认状态
        ];

        $segmentModel->insert($segmentInfo);
    }

    /**
     * 保存订单详情
     *
     * @param int $orderId
     * @param int $passengerId
     * @param array $orderData
     * @return void
     */
    private function saveOrderDetail(int $orderId, int $passengerId, array $orderData): void
    {
        $detailModel = new TicketBookOrderDetailModel();

        $detailInfo = [
            'order_id' => $orderId,
            'order_passenger_id' => $passengerId,
            'ticket_total_price' => $orderData['totalAmount']
        ];

        $detailModel->insert($detailInfo);
    }

    /**
     * 保存票价信息
     *
     * @param int $orderId
     * @param int $passengerId
     * @param array $orderData
     * @return void
     */
    private function saveOrderTicketPrice(int $orderId, int $passengerId, array $orderData): void
    {
        $priceModel = new TicketBookOrderTicketPriceModel();

        $priceInfo = [
            'order_id' => $orderId,
            'order_passenger_id' => $passengerId,
            'passenger_type' => $this->mapPassengerType($orderData['passengerType']),
            'total_amount' => $orderData['totalAmount']
        ];

        $priceModel->insert($priceInfo);
    }

    /**
     * 映射乘客类型
     *
     * @param string $passengerType IBE乘客类型
     * @return int 系统乘客类型
     */
    private function mapPassengerType($passengerType): int
    {
        // IBE乘客类型映射到系统类型
        // D = 成人, C = 儿童, I = 婴儿
        if (empty($passengerType)) {
            return 1; // 默认成人
        }

        switch (strtoupper($passengerType)) {
            case 'D':
            case 'ADT':
                return 1; // 成人
            case 'C':
            case 'CNN':
                return 2; // 儿童
            case 'I':
            case 'INF':
                return 3; // 婴儿
            default:
                return 1; // 默认成人
        }
    }

    /**
     * 解析航班日期
     *
     * @param string $dateString 格式：20221005
     * @return string 格式：2022-10-05
     */
    private function parseFlightDate(string $dateString): string
    {
        if (strlen($dateString) !== 8) {
            throw new \Exception("航班日期格式错误: {$dateString}");
        }

        $year = substr($dateString, 0, 4);
        $month = substr($dateString, 4, 2);
        $day = substr($dateString, 6, 2);

        return "{$year}-{$month}-{$day}";
    }

    /**
     * 构建PNR详情查询请求XML
     *
     * @param string $pnr
     * @param string $office
     * @return string
     */
    private function buildPnrDetailRequestXml(string $pnr, string $office): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<OTA_AirResRetRQ EchoToken="String" TimeStamp="String" Version="String" Target="String">
    <POS>
        <Source PseudoCityCode="' . $office . '" />
    </POS>
    <BookingReferenceID ID="' . $pnr . '">
    </BookingReferenceID>
</OTA_AirResRetRQ>';
    }

    /**
     * 构建票务详情查询请求XML
     *
     * @param string $ticketNumber
     * @param string $office
     * @param string $pnr
     * @return string
     */
    private function buildTicketDetailRequestXml(string $ticketNumber, string $office, string $pnr): string
    {
        return '<?xml version="1.0" encoding="UTF-8"?>
<TES_AirTicketRetRQ>
    <POS>
        <Source PseudoCityCode="' . $office . '"/>
    </POS>
    <TicketNumber>' . $ticketNumber . '</TicketNumber>
    <SecondFactor Code="CN" Value="' . $pnr . '"/>
</TES_AirTicketRetRQ>';
    }

    /**
     * 解析PNR详情响应
     *
     * @param string $responseData
     * @return array
     */
    private function parsePnrDetailResponse(string $responseData): array
    {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($responseData);

        if ($xml === false) {
            $errors = libxml_get_errors();
            throw new \Exception("解析PNR响应XML失败: " . implode(', ', array_column($errors, 'message')));
        }

        $result = [
            'pnr' => (string)$xml->AirResRet->BookingReferenceID['ID'],
            'passengers' => [],
            'segments' => [],
            'contact_info' => '',
            'ticketing_info' => []
        ];

        // 解析乘客信息
        if (isset($xml->AirResRet->AirTraveler)) {
            foreach ($xml->AirResRet->AirTraveler as $traveler) {
                $result['passengers'][] = [
                    'rph' => (string)$traveler['RPH'],
                    'passenger_type' => (string)$traveler['PassengerTypeCode'],
                    'surname' => (string)$traveler->PersonName->Surname,
                    'name_pnr' => (string)$traveler->PersonName->NamePNR,
                    'age' => (int)$traveler->PassengerTypeQuantity['Age']
                ];
            }
        }

        // 解析航段信息
        if (isset($xml->AirResRet->FlightSegments->FlightSegment)) {
            foreach ($xml->AirResRet->FlightSegments->FlightSegment as $segment) {
                $result['segments'][] = [
                    'rph' => (string)$segment['RPH'],
                    'flight_number' => (string)$segment['FlightNumber'],
                    'departure_datetime' => (string)$segment['DepartureDateTime'],
                    'arrival_datetime' => (string)$segment['ArrivalDateTime'],
                    'departure_airport' => (string)$segment->DepartureAirport['LocationCode'],
                    'arrival_airport' => (string)$segment->ArrivalAirport['LocationCode'],
                    'marketing_airline' => (string)$segment->MarketingAirline['Code'],
                    'booking_class' => (string)$segment->BookingClassAvail['ResBookDesigCode'],
                    'status' => (string)$segment['Status'],
                    'passenger_number' => (int)$segment['NumberInParty']
                ];
            }
        }

        // 解析联系信息
        if (isset($xml->AirResRet->ContactInfo)) {
            $result['contact_info'] = (string)$xml->AirResRet->ContactInfo['ContactInfo'];
        }

        // 解析出票信息
        if (isset($xml->AirResRet->Ticketing)) {
            $result['ticketing_info'] = [
                'is_issued' => (string)$xml->AirResRet->Ticketing['IsIssued'] === 'true',
                'office_code' => (string)$xml->AirResRet->Ticketing['OfficeCode'],
                'ticket_time_limit' => (string)$xml->AirResRet->Ticketing['TicketTimeLimit']
            ];
        }

        return $result;
    }

    /**
     * 解析票务详情响应
     *
     * @param string $responseData
     * @return array
     */
    private function parseTicketDetailResponse(string $responseData): array
    {
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($responseData);

        if ($xml === false) {
            $errors = libxml_get_errors();
            throw new \Exception("解析票务响应XML失败: " . implode(', ', array_column($errors, 'message')));
        }

        $result = [
            'passenger' => [],
            'segments' => [],
            'ticketing' => [],
            'pricing' => []
        ];

        // 解析乘客信息
        if (isset($xml->AirTicketRet->AirTraveler)) {
            $result['passenger'] = [
                'passenger_type' => (string)$xml->AirTicketRet->AirTraveler['PassengerTypeCode'],
                'surname' => (string)$xml->AirTicketRet->AirTraveler->PersonName->Surname
            ];
        }

        // 解析航段信息
        if (isset($xml->AirTicketRet->FlightSegments)) {
            foreach ($xml->AirTicketRet->FlightSegments as $segmentGroup) {
                foreach ($segmentGroup->FlightSegment as $segment) {
                    $result['segments'][] = [
                        'rph' => (string)$segment['RPH'],
                        'flight_number' => (string)$segment['FlightNumber'],
                        'departure_datetime' => (string)$segment['DepartureDateTime'],
                        'departure_airport' => (string)$segment->DepartureAirport['LocationCode'],
                        'departure_terminal' => (string)$segment->DepartureAirport['Terminal'],
                        'arrival_airport' => (string)$segment->ArrivalAirport['LocationCode'],
                        'arrival_terminal' => (string)$segment->ArrivalAirport['Terminal'],
                        'marketing_airline' => (string)$segment->MarketingAirline['Code'],
                        'operating_airline' => (string)$segment->OperatingAirline['Code'],
                        'booking_class' => (string)$segment->BookingClassAvail['ResBookDesigCode'],
                        'ticket_status' => (string)$segment['TicketStatus'],
                        'segment_status' => (string)$segment['SegmentStatus'],
                        'pnr_no' => (string)$segment['PnrNo'],
                        'crs_pnr_no' => (string)$segment['CrsPnrNo'],
                        'baggage_weight' => (int)$segment->Baggage['BaggageWeight'],
                        'baggage_piece' => (int)$segment->Baggage['BaggagePiece']
                    ];
                }
            }
        }

        // 解析出票信息
        if (isset($xml->AirTicketRet->Ticketing->TicketItemInfo)) {
            $ticketInfo = $xml->AirTicketRet->Ticketing->TicketItemInfo;
            $result['ticketing'] = [
                'ticket_number' => (string)$ticketInfo['TicketNumber'],
                'total_amount' => (float)$ticketInfo['TotalAmount'],
                'endorsement' => (string)$ticketInfo['Endorsement'],
                'issue_airline' => (string)$ticketInfo['IssueAirline'],
                'org_city' => (string)$ticketInfo['OrgCity'],
                'dst_city' => (string)$ticketInfo['DstCity'],
                'tax' => (float)$ticketInfo['Tax'],
                'eticket_type' => (string)$ticketInfo['ETicketType']
            ];
        }

        // 解析价格信息
        if (isset($xml->AirTicketRet->AirItineraryPricingInfo->ItinTotalFare)) {
            $fareInfo = $xml->AirTicketRet->AirItineraryPricingInfo->ItinTotalFare;
            $result['pricing'] = [
                'base_fare' => [
                    'amount' => (float)$fareInfo->BaseFare['Amount'],
                    'currency' => (string)$fareInfo->BaseFare['CurrencyCode']
                ],
                'taxes' => []
            ];

            // 解析税费信息
            if (isset($fareInfo->Taxes->Tax)) {
                foreach ($fareInfo->Taxes->Tax as $tax) {
                    $result['pricing']['taxes'][] = [
                        'tax_code' => (string)$tax['TaxCode'],
                        'amount' => (float)$tax['Amount'],
                        'currency' => (string)$tax['CurrencyCode']
                    ];
                }
            }
        }

        return $result;
    }

    /**
     * 保存完整的主订单
     *
     * @param array $baseOrder
     * @param array $pnrDetail
     * @param array $ticketDetail
     * @return int
     */
    private function saveCompleteMainOrder(array $baseOrder, array $pnrDetail, array $ticketDetail): int
    {
        $orderModel = new TicketBookOrderModel();

        // 生成订单号
        $orderNo = $orderModel->generate_order_no('T');

        // 解析航班日期
        $flightDate = $this->parseFlightDate($baseOrder['beginFlightDate']);

        // 计算乘客数量和航程类型
        $passengerNumber = count($pnrDetail['passengers']);
        $journeyType = $this->determineJourneyType($pnrDetail['segments']);

        // 构建航程信息
        $journeyInfo = $this->buildJourneyInfo($pnrDetail['segments']);

        // 获取乘客姓名
        $passengerNames = $this->buildPassengerNames($pnrDetail['passengers']);

        $orderInfo = [
            'order_no' => $orderNo,
            'pnr' => $baseOrder['pnr'],
            'journey_type' => $journeyType,
            'journey_info' => $journeyInfo,
            'passenger_number' => $passengerNumber,
            'passenger_names' => $passengerNames,
            'total_supplier_amount' => $baseOrder['totalAmount'],
            'total_customer_amount' => $baseOrder['totalAmount'],
            'status' => 2, // 已出票
            'area_type' => TicketBookOrderModel::ORDER_AREA_INTL, // 国际
            'order_source' => 5, // B2B订单
            'customer_type' => 1, // 自有客户
            'contact_name' => $this->extractContactName($pnrDetail['contact_info']),
            'contact_telephone' => $this->extractContactPhone($pnrDetail['contact_info']),
            'office' => $pnrDetail['ticketing_info']['office_code'] ?? config('IBE')->office ?? 'BJS191'
        ];

        return $orderModel->insert($orderInfo);
    }

    /**
     * 保存完整的乘客信息
     *
     * @param int $orderId
     * @param array $pnrDetail
     * @param array $ticketDetail
     * @return array 乘客ID数组
     */
    private function saveCompleteOrderPassengers(int $orderId, array $pnrDetail, array $ticketDetail): array
    {
        $passengerModel = new TicketBookOrderPassengerModel();
        $passengerIds = [];

        foreach ($pnrDetail['passengers'] as $passenger) {
            $passengerInfo = [
                'order_id' => $orderId,
                'rph' => $passenger['rph'],
                'person_name' => $passenger['surname'],
                'passenger_type' => $this->mapPassengerType($passenger['passenger_type']),
                'ticket_number' => $ticketDetail['ticketing']['ticket_number'] ?? ''
            ];

            $passengerId = $passengerModel->insert($passengerInfo);
            $passengerIds[] = $passengerId;
        }

        return $passengerIds;
    }

    /**
     * 保存完整的航段信息
     *
     * @param int $orderId
     * @param array $pnrDetail
     * @param array $ticketDetail
     * @return void
     */
    private function saveCompleteOrderSegments(int $orderId, array $pnrDetail, array $ticketDetail): void
    {
        $segmentModel = new TicketBookOrderSegmentModel();

        if (empty($pnrDetail['segments'])) {
            throw new \Exception("PNR详情中没有航段信息");
        }

        foreach ($pnrDetail['segments'] as $segment) {
            $segmentInfo = [
                'order_id' => $orderId,
                'rph' => $segment['rph'],
                'departure_datetime' => $this->formatDateTime($segment['departure_datetime']),
                'arrival_datetime' => $this->formatDateTime($segment['arrival_datetime']),
                'airline' => $segment['marketing_airline'],
                'flight_number' => $segment['flight_number'],
                'operating_airline' => $segment['marketing_airline'],
                'operating_flight_number' => $segment['flight_number'],
                'cabin' => $segment['booking_class'],
                'passenger_number' => $segment['passenger_number'],
                'action_code' => $segment['status']
            ];

            $result = $segmentModel->insert($segmentInfo);
            if (!$result) {
                throw new \Exception("保存航段信息失败: " . json_encode($segmentInfo));
            }
        }
    }

    /**
     * 保存完整的订单详情和票价信息
     *
     * @param int $orderId
     * @param array $passengerIds
     * @param array $ticketDetail
     * @return void
     */
    private function saveCompleteOrderDetails(int $orderId, array $passengerIds, array $ticketDetail): void
    {
        $detailModel = new TicketBookOrderDetailModel();

        foreach ($passengerIds as $passengerId) {
            // 保存订单详情
            $detailInfo = [
                'order_id' => $orderId,
                'order_passenger_id' => $passengerId,
                'ticket_total_price' => $ticketDetail['ticketing']['total_amount'] ?? 0
            ];
            $detailModel->insert($detailInfo);

            // 票价信息表不存在，暂时跳过
            // TODO: 如果需要保存票价信息，需要先创建对应的数据库表
        }
    }

    /**
     * 确定航程类型
     *
     * @param array $segments
     * @return int
     */
    private function determineJourneyType(array $segments): int
    {
        $segmentCount = count($segments);

        if ($segmentCount == 1) {
            return 1; // 单程
        } elseif ($segmentCount == 2) {
            // 检查是否为往返
            $firstSegment = $segments[0];
            $lastSegment = $segments[$segmentCount - 1];

            if ($firstSegment['departure_airport'] == $lastSegment['arrival_airport'] &&
                $firstSegment['arrival_airport'] == $lastSegment['departure_airport']) {
                return 2; // 往返
            } else {
                return 3; // 联程-两航段
            }
        } elseif ($segmentCount <= 4) {
            return 4; // 联程-多航段
        } else {
            return 5; // 多航段
        }
    }

    /**
     * 构建航程信息
     *
     * @param array $segments
     * @return string
     */
    private function buildJourneyInfo(array $segments): string
    {
        if (empty($segments)) {
            return '';
        }

        $airports = [];
        $airports[] = $segments[0]['departure_airport'];

        foreach ($segments as $segment) {
            $airports[] = $segment['arrival_airport'];
        }

        return implode('', $airports);
    }

    /**
     * 构建乘客姓名字符串
     *
     * @param array $passengers
     * @return string
     */
    private function buildPassengerNames(array $passengers): string
    {
        $names = [];
        foreach ($passengers as $passenger) {
            $names[] = $passenger['surname'];
        }
        return implode(',', $names);
    }

    /**
     * 提取联系人姓名
     *
     * @param string $contactInfo
     * @return string
     */
    private function extractContactName(string $contactInfo): string
    {
        // 从联系信息中提取姓名，这里简化处理
        if (preg_match('/\/([A-Z\s]+)\//', $contactInfo, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }

    /**
     * 提取联系电话
     *
     * @param string $contactInfo
     * @return string
     */
    private function extractContactPhone(string $contactInfo): string
    {
        // 从联系信息中提取电话，这里简化处理
        if (preg_match('/(\d{3,4}-\d{7,8}|\d{11})/', $contactInfo, $matches)) {
            return $matches[1];
        }
        return '';
    }

    /**
     * 格式化日期时间
     *
     * @param string $dateTime
     * @return string
     */
    private function formatDateTime(string $dateTime): string
    {
        try {
            $dt = new \DateTime($dateTime);
            return $dt->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            // 如果解析失败，返回默认值
            return date('Y-m-d H:i:s');
        }
    }
}
