<?php

namespace App\Services\Intl;

use App\Helpers\Tools\Char;
use App\Helpers\Tools\Idcard;
use App\Helpers\Tools\Time;
use App\Models\OrderModel;
use App\Models\PnrPassengerModel;
use App\Models\PnrTicketSegmentModel;
use App\Models\TicketBookOrderModel;
use App\Models\TicketBookOrderPassengerModel;
use App\Services\BaseService;

/**
 * 国际机票基础服务类
 *
 * 合并了 BaseOrder 和 IntlOrderDataService 的功能
 * 提供国际机票业务的所有基础服务
 */
abstract class IntlBaseService extends BaseService
{
    /**
     * 缓存的基础数据
     */
    private array $airports    = [];
    private array $airlines    = [];
    private array $users       = [];
    private array $departments = [];
    private array $cabins      = [];

    /**
     * 抽象方法 - 由子类返回订单模型实例
     * @return \CodeIgniter\Model
     */
    abstract protected function getOrderModel();

    /**
     * 抽象方法 - 由子类返回订单类型标识
     * @return string
     */
    abstract protected function getOrderType(): string;

    // ==================== 订单列表查询功能 ====================

    /**
     * 获取订单列表
     *
     * @param  array  $params
     *
     * @return array
     */
    public function getOrderList(array $params)
    {
        $page    = $params['page'] ?? 1;
        $perPage = $params['per_page'] ?? 10;

        // 获取子类指定的模型
        $orderModel = $this->getOrderModel();
        // 获取子类指定的订单类型
        $orderWhere = $this->handleWhere($params, $this->getOrderType());

        // 分页查询
        $list = $orderModel->paginate_list($orderWhere, $page, $perPage);
        $pager = $orderModel->pager;

        // 分组统计
        $totalStatus = $orderModel->total_status($orderWhere);

        return [
            'list'         => $list,
            'total_status' => $totalStatus,
            'total'        => $pager->getTotal(),
            'perPage'      => $pager->getPerPage(),
            'pageCount'    => $pager->getPageCount(),
            'currentPage'  => $pager->getCurrentPage(),
        ];
    }

    /**
     * 处理查询条件（可被子类覆盖）
     *
     * @param  array  $params
     * @param  string  $type
     *
     * @return array
     */
    protected function handleWhere(array $params, string $type): array
    {
        $data = [
            'list'         => [],
            'total_status' => [],
            'total'        => 0,
            'perPage'      => 0,
            'pageCount'    => 0,
            'currentPage'  => 0,
        ];

        $orderPassengerWhere = [];
        $pnrSegmentWhere     = [];
        // 这边只查国际订单
        $orderWhere = [
            'area_type' => TicketBookOrderModel::ORDER_AREA_INTL,
        ];

        if (!empty($params['pnr'])) {
            $orderWhere['pnr'] = trim($params['pnr']);
        }
        if (!empty($params['order_status'])) {
            $orderWhere['status'] = $params['order_status'];
        }
        if (!empty($params['journey_type'])) {
            $orderWhere['journey_type'] = intval($params['journey_type']);
        }
        if (!empty($params['order_no'])) {
            $orderWhere['order_no'] = trim($params['order_no']);
        }
        if (!empty($params['origin_order_no'])) {
            $orderWhere['order_no'] = trim($params['origin_order_no']);
        }
        if (!empty($params['customer_type'])) {
            $orderWhere['customer_type'] = intval($params['customer_type']);
        }
        if (!empty($params['order_source'])) {
            $orderWhere['order_source'] = intval($params['order_source']);
        }
        if (!empty($params['operator_name'])) {
            $orderWhere['operator_name'] = trim($params['operator_name']);
        }
        if (!empty($params['order_date_start'])) {
            $orderWhere['created_at_from'] = $params['order_date_start'];
        }
        if (!empty($params['order_date_end'])) {
            $orderWhere['created_at_to'] = $params['order_date_end'];
        }
        if (!empty($params['ticket_number'])) {
            $orderPassengerWhere['ticket_number'] = trim($params['ticket_number']);
        }
        if (!empty($params['person_name'])) {
            $orderPassengerWhere['person_name'] = trim($params['person_name']);
        }
        if (!empty($params['airline'])) {
            $pnrSegmentWhere['airline'] = trim($params['airline']);
        }
        if (!empty($params['flight_number'])) {
            $pnrSegmentWhere['flight_number'] = trim($params['flight_number']);
        }
        if (!empty($params['departure_date_start'])) {
            $pnrSegmentWhere['departure_datetime >='] = $params['departure_date_start'];
        }
        if (!empty($params['departure_date_end'])) {
            $pnrSegmentWhere['departure_datetime <='] = $params['departure_date_end'];
        }

        if ($type == 'order') {
            $orderPassengerModel = model('TicketBookOrderPassengerModel');
            $pnrSegmentModel     = model('TicketBookOrderSegmentModel');
        } else {
            $orderPassengerModel = model('TicketRefundOrderPassengerModel');
            $pnrSegmentModel     = model('TicketRefundOrderSegmentModel');
        }

        if (!empty($orderPassengerWhere)) {
            $orderPassengers = $orderPassengerModel->where($orderPassengerWhere)->findAll();
            if (empty($orderPassengers)) {
                success('成功', $data);
            }
            $orderWhere['ids'] = array_column($orderPassengers, 'order_id');
        }
        if (!empty($pnrSegmentWhere)) {
            foreach ($pnrSegmentWhere as $k => $v) {
                $pnrSegmentModel->where($k, $v);
            }
            $pnrSegments = $pnrSegmentModel->findAll();
            if (empty($pnrSegments)) {
                success('成功', $data);
            }
            $orderWhere['ids'] = array_merge($orderWhere['ids'] ?? [], array_column($pnrSegments, 'order_id'));
        }

        return $orderWhere;
    }

    // ==================== 基础数据查询和缓存功能 ====================

    /**
     * 获取并缓存机场数据
     *
     * @param  array  $airportCodes  机场代码数组
     *
     * @return array 机场数据，以机场代码为键
     */
    public function getAirports(array $airportCodes = []): array
    {
        if (empty($this->airports) || !empty($airportCodes)) {
            $airportModel = model('AirportModel');
            $query        = $airportModel->select('id,airport_code,city_cn,airport_name_cn');

            if (!empty($airportCodes)) {
                $query->whereIn('airport_code', array_unique($airportCodes));
            }

            $airports       = $query->findAll();
            $this->airports = array_merge($this->airports, array_column($airports, null, 'airport_code'));
        }

        return $this->airports;
    }

    /**
     * 获取并缓存航空公司数据
     *
     * @param  array  $airlineCodes  航空公司代码数组
     *
     * @return array 航空公司数据，以航空公司代码为键
     */
    public function getAirlines(array $airlineCodes = []): array
    {
        if (empty($this->airlines) || !empty($airlineCodes)) {
            $airlineModel = model('AirlineModel');
            $query        = $airlineModel->select('id,airline_code,airline_cn');

            if (!empty($airlineCodes)) {
                $query->whereIn('airline_code', array_unique($airlineCodes));
            }

            $airlines       = $query->findAll();
            $this->airlines = array_merge($this->airlines, array_column($airlines, null, 'airline_code'));
        }

        return $this->airlines;
    }

    /**
     * 获取用户信息
     *
     * @param  int  $userId  用户ID
     *
     * @return array|null 用户信息
     */
    public function getUser(int $userId): ?array
    {
        if (!isset($this->users[$userId])) {
            $userModel            = model('UserModel');
            $this->users[$userId] = $userModel->find($userId);
        }

        return $this->users[$userId];
    }

    /**
     * 获取部门信息
     *
     * @param  int  $departmentId  部门ID
     *
     * @return array|null 部门信息
     */
    public function getDepartment(int $departmentId): ?array
    {
        if (!isset($this->departments[$departmentId])) {
            $departmentModel                  = model('DepartmentModel');
            $this->departments[$departmentId] = $departmentModel->find($departmentId);
        }

        return $this->departments[$departmentId];
    }

    /**
     * 获取舱位信息
     *
     * @param  string  $airlineCode  航空公司代码
     * @param  string  $cabin  舱位代码
     *
     * @return array|null 舱位信息
     */
    public function getCabin(string $airlineCode, string $cabin): ?array
    {
        $key = $airlineCode . '_' . $cabin;
        if (!isset($this->cabins[$key])) {
            $cabinModel         = model('CabinModel');
            $this->cabins[$key] = $cabinModel->where(['airline' => $airlineCode, 'cabin' => $cabin])->first();
        }

        return $this->cabins[$key];
    }

    // ==================== 航程名称生成功能 ====================

    /**
     * 生成航程名称（与confirmOrder相同的逻辑）
     *
     * @param  int  $journeyType
     * @param  array  $segments
     *
     * @return array
     */
    public function generateJourneyNames(int $journeyType, array $segments): array
    {
        $names = [];
        foreach ($segments as $index => $segment) {
            $rph = $segment['rph'] ?? $index + 1;

            switch ($journeyType) {
                case 1: // 单程
                    $names[$index] = '单程';
                    break;
                case 2: // 往返
                    if ($rph == 1) {
                        $names[$index] = '去程';
                    } else {
                        $names[$index] = '返程';
                    }
                    break;
                case 3: // 联程
                case 4:
                case 5:
                    $names[$index] = '第' . Char::to_chinase_num($rph) . '程';
                    break;
                default:
                    $names[$index] = '';
            }
        }

        return $names;
    }

    // ==================== 订单数据构建功能 ====================

    /**
     * 构建订单基础信息
     *
     * @param  array  $order  订单数据
     * @param  string  $type  订单类型 ('order', 'refund')
     *
     * @return array 格式化的订单信息
     */
    public function buildOrderData(array $order, string $type = 'order'): array
    {
        $user       = $this->getUser($order['operator_id']);
        $department = $this->getDepartment($user['department_id']);

        $orderData = [
            'order_no'          => $order['order_no'],
            'status'            => $order['status'],
            'status_text'       => OrderModel::ORDER_STATUS[$order['status']],
            'pnr'               => $order['pnr'],
            'customer_type'     => OrderModel::CUSTOMER_TYPES[$order['customer_type']],
            'created_at'        => date('Y-m-d H:i:s', $order['created_at']),
            'operator_name'     => $department['department_name'] . '|' . $user['name'],
            'journey_type'      => $order['journey_type'],
            'journey_type_text' => OrderModel::JOURNEY_TYPES[$order['journey_type']],
        ];

        // 根据订单类型添加特定字段
        if ($type === 'order') {
            $orderData['total_supplier_amount'] = $order['total_supplier_amount'];
            $orderData['total_customer_amount'] = $order['total_customer_amount'];
            $orderData['order_source']          = OrderModel::ORDER_SOURCES[$order['order_source']];
        } elseif ($type === 'refund') {
            $orderData['relate_order_no']    = $order['origin_order_no'];
            $orderData['refund_status']      = $order['status'];
            $orderData['refund_status_text'] = OrderModel::REFUND_STATUS[$order['status']];
            $orderData['refund_type']        = $order['refund_type'];
            $orderData['refund_type_text']   = OrderModel::REFUND_TYPE[$order['refund_type']];
            $orderData['contact_name']       = $order['contact_name'];
            $orderData['contact_telephone']  = $order['contact_telephone'];
            $orderData['contact_email']      = $order['contact_email'];
            $orderData['is_send_sms']        = $order['is_send_sms'];
            $orderData['remark']             = $order['remark'];
            $orderData['refunded_at']        = date('Y-m-d H:i:s', $order['created_at']);
        }

        return $orderData;
    }

    /**
     * 构建航班信息
     *
     * @param  array  $orderSegments  订单航段数据
     * @param  int  $journeyType  航程类型
     *
     * @return array 格式化的航班信息
     * @throws \Exception
     */
    public function buildFlightData(array $orderSegments, int $journeyType): array
    {
        if (empty($orderSegments)) {
            return [];
        }

        // 获取航班信息
        $flightModel = model('FlightModel');
        $flights     = $flightModel->whereIn('flight_number', array_unique(array_column($orderSegments, 'flight_number')))->findAll();
        $flights     = array_column($flights, null, 'flight_number');

        // 收集机场和航空公司代码
        $airportCodes = [];
        $airlineCodes = [];
        foreach ($flights as $flight) {
            $airportCodes[] = $flight['departure_airport'];
            $airportCodes[] = $flight['arrival_airport'];
            $airlineCodes[] = $flight['airline_code'];
        }

        // 获取基础数据
        $airports = $this->getAirports($airportCodes);
        $airlines = $this->getAirlines($airlineCodes);

        // 生成航程名称
        $journeyNames = $this->generateJourneyNames($journeyType, $orderSegments);

        $flightData    = [];
        $totalDuration = 0;

        foreach ($orderSegments as $index => $segment) {
            if (empty($flights[$segment['flight_number']])) {
                continue;
            }

            $flight      = $flights[$segment['flight_number']];
            $airlineCode = $flight['airline_code'];

            // 飞行时长计算
            $departureObj  = new \CodeIgniter\I18n\Time($segment['departure_datetime']);
            $arrivalObj    = new \CodeIgniter\I18n\Time($segment['arrival_datetime']);
            $duration      = $departureObj->difference($arrivalObj)->getMinutes();
            $totalDuration += $duration;

            // 格式化时长
            $hours    = floor($duration / 60);
            $minutes  = $duration % 60;
            $interval = ($hours ? $hours . 'h' : '') . ($minutes ? $minutes . 'm' : '');

            // 获取舱位信息
            $cabin = $this->getCabin($airlineCode, $segment['cabin']);

            $flightData[] = [
                'order_segment_id'     => $segment['id'], // 航段ID
                'journey_name'         => $journeyNames[$index] ?? '未知',
                'airline_code'         => $airlineCode,
                'airline_logo'         => "/static/images/airline/{$airlineCode}.png",
                'flight_number'        => $segment['flight_number'],
                'airline_cn'           => $airlines[$airlineCode]['airline_cn'] ?? '',
                'air_equip_type'       => $flight['air_equip_type'] ?? '',
                'departure_airport'    => $flight['departure_airport'],
                'departure_airport_cn' => $airports[$flight['departure_airport']]['city_cn'] . $airports[$flight['departure_airport']]['airport_name_cn'],
                'arrival_airport'      => $flight['arrival_airport'],
                'arrival_airport_cn'   => $airports[$flight['arrival_airport']]['city_cn'] . $airports[$flight['arrival_airport']]['airport_name_cn'],
                'departure_date'       => $departureObj->toDateString(),
                'departure_time'       => $departureObj->format('H:i'),
                'arrival_date'         => $arrivalObj->toDateString(),
                'arrival_time'         => $arrivalObj->format('H:i'),
                'interval'             => $interval,
                'duration'             => $duration,
                'cabin'                => $segment['cabin'],
                'discount'             => ($cabin['discount'] ?? '') . '折',
                'luggage_weight'       => $cabin['luggage_weight'] ?? '',
                'meal'                 => '有餐食', // TODO: 需要计算
                'refund_rate'          => '退票10%-45%', // TODO: 需要计算
                'sub_cabin'            => $segment['sub_cabin'],
                'customer_explain'     => '退票规定:起飞前7天《含)之前:Q舱的20%，起飞前7天(不含)之内至起飞前2天(含)之前:Q舱的30%，起飞前2天(不含)之内至起飞前4小时……',
            ];
        }

        // 处理中转信息
        $shopping   = load_service('Intl\Shopping');
        $flightData = $shopping->handleSegmentTransit($flightData, $airports);

        // 构建外层航班信息
        $firstFlight   = $flightData[0];
        $lastFlight    = $flightData[count($flightData) - 1];
        $totalInterval = Time::computeHoursByDuration($totalDuration);

        return [
            [
                'airline_code'         => $firstFlight['airline_code'],
                'flight_number'        => $firstFlight['flight_number'],
                'air_equip_type'       => $firstFlight['air_equip_type'],
                'departure_airport'    => $firstFlight['departure_airport'],
                'departure_airport_cn' => $firstFlight['departure_airport_cn'],
                'departure_date'       => $firstFlight['departure_date'],
                'departure_time'       => $firstFlight['departure_time'],
                'arrival_airport'      => $lastFlight['arrival_airport'],
                'arrival_airport_cn'   => $lastFlight['arrival_airport_cn'],
                'arrival_date'         => $lastFlight['arrival_date'],
                'arrival_time'         => $lastFlight['arrival_time'],
                'duration'             => $totalDuration,
                'interval'             => $totalInterval['hours'] . 'h' . $totalInterval['minutes'] . 'm',
                'is_direct'            => count($flightData) == 1,
                'list'                 => $flightData,
            ],
        ];
    }

    // ==================== 乘客信息构建功能 ====================

    /**
     * 构建乘客信息（用于退款申请和退款详情）
     *
     * @param  int  $orderId  订单ID
     * @param  string  $type  类型 ('apply', 'detail')
     * @param  string  $passengerModel  乘客模型名称
     * @param  string  $segmentModel  航段模型名称
     *
     * @return array 格式化的乘客信息
     */
    public function buildPassengerData(int $orderId, string $type = 'apply', string $passengerModel = 'TicketBookOrderPassengerModel', string $segmentModel = 'TicketBookOrderPassengerSegmentModel'): array
    {
        $orderPassengerModel        = model($passengerModel);
        $orderPassengerSegmentModel = model($segmentModel);

        $orderPassengers   = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $orderPassengerIds = array_column($orderPassengers, 'id');

        if (empty($orderPassengerIds)) {
            return [];
        }

        // 获取乘客航段信息
        $orderPassengerSegments = $orderPassengerSegmentModel->whereIn('passenger_id', $orderPassengerIds)->findAll();

        // 按ticket_id分组航段信息
        $passengerSegmentArr = [];
        $allowFlightNumbers  = [];

        foreach ($orderPassengerSegments as $segment) {
            $ticketId                         = $segment['passenger_id'];
            $passengerSegmentArr[$ticketId][] = [
                'ticket_id'     => $ticketId,
                'flight_number' => $segment['flight_number'],
                'status'        => $segment['status'],
                'status_text'   => PnrTicketSegmentModel::STATUS[$segment['status']],
            ];

            if ($segment['status'] == 1) {
                $allowFlightNumbers[$ticketId][] = $segment['flight_number'];
            }
        }

        $passengerData = [];
        foreach ($orderPassengers as $passenger) {
            $ticketId     = $passenger['id'];
            $ticketNumber = $passenger['ticket_number'];

            // 计算退改状态
            $refundChangeStatus = $this->calculateRefundChangeStatus($passengerSegmentArr[$ticketId] ?? []);

            $passengerInfo = [
                'rph'                  => $passenger['rph'],
                'ticket_id'            => $ticketId,
                'pnr_passenger_id'     => $passenger['id'],
                'order_id'             => $orderId,
                'person_name'          => $passenger['person_name'],
                'gender'               => PnrPassengerModel::GENDER[$passenger['gender']],
                'passenger_type'       => $passenger['passenger_type'],
                'passenger_type_text'  => PnrPassengerModel::PASSENGER_TYPE[$passenger['passenger_type']],
                'doc_type'             => $passenger['doc_type'],
                'doc_type_text'        => PnrPassengerModel::DOC_TYPE[$passenger['doc_type']],
                'doc_id'               => Idcard::mask($passenger['doc_id']),
                'telephone'            => Char::mask_phone($passenger['telephone']),
                'ticket_number'        => Char::mask_ticket($ticketNumber),
                'refund_change_status' => $refundChangeStatus,
            ];

            if ($type === 'apply') {
                $passengerInfo['ticket_segments']      = $passengerSegmentArr[$ticketId] ?? [];
                $passengerInfo['allow_flight_numbers'] = $allowFlightNumbers[$ticketId] ?? [];
            } elseif ($type === 'detail') {
                $passengerInfo['ticket_segments']      = $passengerSegmentArr;
                $passengerInfo['allow_flight_numbers'] = $allowFlightNumbers[$ticketId] ?? [];
            }

            $passengerData[] = $passengerInfo;
        }

        return $passengerData;
    }

    /**
     * 计算退改状态
     *
     * @param  array  $ticketSegments  票号对应的航段数组
     *
     * @return string 退改状态
     */
    private function calculateRefundChangeStatus(array $ticketSegments): string
    {
        if (empty($ticketSegments)) {
            return '不可退';
        }

        $availableQty = 0; // 可退票数量
        $refundedQty  = 0;  // 已退票数

        foreach ($ticketSegments as $segment) {
            if ($segment['status'] == 1) {
                $availableQty++;
            } elseif ($segment['status'] == 5) {
                $refundedQty++;
            }
        }

        if ($availableQty >= 1) {
            return '可退票';
        } elseif ($refundedQty == count($ticketSegments)) {
            return '已退票';
        } else {
            return '不可退';
        }
    }

    /**
     * 构建订单详情的乘客信息（包含价格信息）
     *
     * @param  int  $orderId  订单ID
     *
     * @return array 格式化的乘客信息
     */
    public function buildOrderDetailPassengerData(int $orderId): array
    {
        $orderPassengerModel = model('TicketBookOrderPassengerModel');
        $orderDetailModel    = model('TicketBookOrderDetailModel');

        $orderPassengers   = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $orderPassengerIds = array_column($orderPassengers, 'id');

        if (empty($orderPassengerIds)) {
            return [];
        }

        $orderDetail     = $orderDetailModel->whereIn('order_passenger_id', $orderPassengerIds)->findAll();
        $orderDetail     = array_column($orderDetail, null, 'order_passenger_id');
        $orderPassengers = array_column($orderPassengers, null, 'id');

        $passengerData = [];
        foreach ($orderPassengers as $passenger) {
            $orderPassengerId = $passenger['id'];
            $ticketNumber     = $passenger['ticket_number'];
            $ticketNumber     = $ticketNumber ? Char::mask_ticket($ticketNumber) : '';
            $detail           = $orderDetail[$orderPassengerId];

            // 计算各种费用
            $totalSupplier      = 0.00;
            $totalCustomer      = 0.00;
            $rebate             = 0.00;
            $supplierServiceFee = 0.00;
            $customerServiceFee = 0.00;
            $insurance          = 0.00;
            $insuranceBonus     = 0.00;
            $concessions        = 0.00;

            // 采购总计计算
            $totalSupplier = bcadd($totalSupplier, $detail['customer_amount'], 2);
            $totalSupplier = bcsub($totalSupplier, $detail['ticket_supplier_agency_fee'], 2);
            $totalSupplier = bcsub($totalSupplier, $rebate, 2);
            $totalSupplier = bcadd($totalSupplier, $supplierServiceFee, 2);
            $totalSupplier = bcadd($totalSupplier, $insurance, 2);
            $totalSupplier = bcsub($totalSupplier, $insuranceBonus, 2);

            // 销售总计计算
            $totalCustomer = bcadd($totalCustomer, $detail['customer_amount'], 2);
            $totalCustomer = bcsub($totalCustomer, $concessions, 2);
            $totalCustomer = bcadd($totalCustomer, $customerServiceFee, 2);
            $totalCustomer = bcadd($totalCustomer, $insurance, 2);

            $passengerData[] = [
                'order_id'            => $orderId,
                'rph'                 => $passenger['rph'],
                'person_name'         => $passenger['person_name'],
                'passenger_type'      => $passenger['passenger_type'],
                'passenger_type_text' => PnrPassengerModel::PASSENGER_TYPE[$passenger['passenger_type']],
                'doc_type'            => $passenger['doc_type'],
                'doc_type_text'       => PnrPassengerModel::DOC_TYPE[$passenger['doc_type']],
                'doc_id'              => $passenger['doc_id'],
                'telephone'           => $passenger['telephone'],
                'ticket_number'       => $ticketNumber,
                'is_free'             => $detail['is_free'],
                'customer_price'      => $detail['ticket_marketing_price'],
                'tax_cn'              => $detail['ticket_tax_cn'],
                'tax_yq'              => $detail['ticket_tax_yq'],
                'tax_xt'              => $detail['ticket_tax_xt'],
                'customer_amount'     => $detail['customer_amount'],
                'supplier_agency_fee' => $detail['ticket_supplier_agency_fee'],
                'price1'              => $rebate,
                'price2'              => $supplierServiceFee,
                'price3'              => $concessions,
                'price4'              => $customerServiceFee,
                'price5'              => $insurance,
                'price6'              => $insuranceBonus,
                'total_supplier'      => $totalSupplier,
                'total_customer'      => $totalCustomer,
            ];
        }

        return $passengerData;
    }

    // ==================== 价格信息构建功能 ====================

    /**
     * 构建退款订单的价格信息
     *
     * @param  int  $orderId  订单ID
     *
     * @return array 格式化的价格信息
     */
    public function buildRefundPriceData(int $orderId): array
    {
        $orderDetailModel          = model('TicketRefundOrderDetailModel');
        $refundOrderPassengerModel = model('TicketRefundOrderPassengerModel');

        $orderDetail     = $orderDetailModel->where('order_id', $orderId)->findAll();
        $orderPassengers = $refundOrderPassengerModel->where('order_id', $orderId)->findAll();
        $orderPassengers = array_column($orderPassengers, null, 'id');

        $priceData = [
            'purchase' => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'              => 0.00,
                    'total_tax_cn'                      => 0.00,
                    'total_tax_yq'                      => 0.00,
                    'total_tax_xt'                      => 0.00,
                    'total_supplier_agency_fee'         => 0.00,
                    'total_customer_amount'             => 0.00,
                    'total_purchase_amount'             => 0.00,
                    'total_supplier_refund_fee_percent' => 0.00,
                    'total_service_fee'                 => 0.00,
                    'total_deduction'                   => 0.00,
                    'total_refunded_amount'             => 0.00,
                ],
            ],
            'sales'    => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'              => 0.00,
                    'total_tax_cn'                      => 0.00,
                    'total_tax_yq'                      => 0.00,
                    'total_tax_xt'                      => 0.00,
                    'total_customer_amount'             => 0.00,
                    'total_customer_refund_fee_percent' => 0.00,
                    'total_service_fee'                 => 0.00,
                    'total_deduction'                   => 0.00,
                    'total_customer_reward'             => 0.00,
                    'total_total_receivable'            => 0.00,
                    'total_refunded_amount'             => 0.00,
                ],
            ],
        ];

        foreach ($orderDetail as $detail) {
            $orderPassenger = $orderPassengers[$detail['order_passenger_id']];

            // 采购价格信息
            $supplierPrice        = bcadd($detail['ticket_marketing_price'], $detail['ticket_supplier_deduction_fee'], 2);
            $supplierRefundAmount = $detail['ticket_marketing_price'] + $detail['ticket_tax_cn'] + $detail['ticket_tax_yq'] - $detail['ticket_supplier_deduction_fee'];

            $priceData['purchase']['detail'][] = [
                'order_id'                    => $orderPassenger['order_id'],
                'ticket_id'                   => $orderPassenger['id'],
                'ticket_number'               => $orderPassenger['ticket_number'],
                'person_name'                 => $orderPassenger['person_name'],
                'supplier_price'              => $supplierPrice,
                'tax_cn'                      => $detail['ticket_tax_cn'],
                'tax_yq'                      => $detail['ticket_tax_yq'],
                'tax_xt'                      => $detail['ticket_tax_xt'],
                'customer_amount'             => $detail['customer_amount'],
                'supplier_agency_fee'         => 0,
                'supplier_amount'             => $supplierPrice,
                'customer_reward'             => '-',
                'total_receivable'            => '-',
                'supplier_refund_fee_percent' => $detail['ticket_supplier_deduction_fee_rate'],
                'service_fee'                 => $detail['ticket_supplier_service_fee'],
                'deduction'                   => $detail['ticket_supplier_deduction_fee'],
                'total_refunded_amount'       => $supplierRefundAmount,
            ];

            // 销售价格信息
            $totalReceivable      = bcsub($detail['ticket_marketing_price'], 0, 2);
            $customerRefundAmount = $detail['ticket_marketing_price'] + $detail['ticket_tax_cn'] + $detail['ticket_tax_yq'] - $detail['ticket_supplier_deduction_fee'];

            $priceData['sales']['detail'][] = [
                'order_id'                    => $orderPassenger['order_id'],
                'ticket_id'                   => $orderPassenger['id'],
                'ticket_number'               => $orderPassenger['ticket_number'],
                'person_name'                 => $orderPassenger['person_name'],
                'customer_price'              => $detail['ticket_marketing_price'],
                'tax_cn'                      => $detail['ticket_tax_cn'],
                'tax_yq'                      => $detail['ticket_tax_yq'],
                'tax_xt'                      => $detail['ticket_tax_xt'],
                'customer_amount'             => $detail['ticket_marketing_price'],
                'supplier_agency_fee'         => '-',
                'supplier_price'              => '-',
                'customer_reward'             => 0,
                'total_receivable'            => $totalReceivable,
                'customer_refund_fee_percent' => $detail['ticket_customer_deduction_fee_rate'],
                'service_fee'                 => $detail['ticket_customer_service_fee'],
                'deduction'                   => $detail['ticket_supplier_deduction_fee'],
                'total_refunded_amount'       => $customerRefundAmount,
            ];

            // 累计采购总计
            $priceData['purchase']['total']['total_customer_price']              = bcadd($priceData['purchase']['total']['total_customer_price'], $detail['ticket_marketing_price'], 2);
            $priceData['purchase']['total']['total_tax_cn']                      = bcadd($priceData['purchase']['total']['total_tax_cn'], $detail['ticket_tax_cn'], 2);
            $priceData['purchase']['total']['total_tax_yq']                      = bcadd($priceData['purchase']['total']['total_tax_yq'], $detail['ticket_tax_yq'], 2);
            $priceData['purchase']['total']['total_tax_xt']                      = bcadd($priceData['purchase']['total']['total_tax_xt'], $detail['ticket_tax_xt'], 2);
            $priceData['purchase']['total']['total_customer_amount']             = bcadd($priceData['purchase']['total']['total_customer_amount'], $detail['customer_amount'], 2);
            $priceData['purchase']['total']['total_supplier_agency_fee']         = bcadd($priceData['purchase']['total']['total_supplier_agency_fee'], 0, 2);
            $priceData['purchase']['total']['total_purchase_amount']             = bcadd($priceData['purchase']['total']['total_purchase_amount'], $detail['ticket_marketing_price'], 2);
            $priceData['purchase']['total']['total_supplier_refund_fee_percent'] = $detail['ticket_supplier_deduction_fee_rate'];
            $priceData['purchase']['total']['total_service_fee']                 = bcadd($priceData['purchase']['total']['total_service_fee'], $detail['ticket_supplier_service_fee'], 2);
            $priceData['purchase']['total']['total_deduction']                   = bcadd($priceData['purchase']['total']['total_deduction'], $detail['ticket_supplier_deduction_fee'], 2);
            $priceData['purchase']['total']['total_refunded_amount']             = bcadd($priceData['purchase']['total']['total_refunded_amount'], $supplierRefundAmount, 2);

            // 累计销售总计
            $priceData['sales']['total']['total_customer_price']              = bcadd($priceData['sales']['total']['total_customer_price'], $detail['ticket_marketing_price'], 2);
            $priceData['sales']['total']['total_tax_cn']                      = bcadd($priceData['sales']['total']['total_tax_cn'], $detail['ticket_tax_cn'], 2);
            $priceData['sales']['total']['total_tax_yq']                      = bcadd($priceData['sales']['total']['total_tax_yq'], $detail['ticket_tax_yq'], 2);
            $priceData['sales']['total']['total_tax_xt']                      = bcadd($priceData['sales']['total']['total_tax_xt'], $detail['ticket_tax_xt'], 2);
            $priceData['sales']['total']['total_customer_amount']             = bcadd($priceData['sales']['total']['total_customer_amount'], $detail['customer_amount'], 2);
            $priceData['sales']['total']['total_customer_reward']             = bcadd($priceData['sales']['total']['total_customer_reward'], 0, 2);
            $priceData['sales']['total']['total_customer_refund_fee_percent'] = $detail['ticket_customer_deduction_fee_rate'];
            $priceData['sales']['total']['total_service_fee']                 = bcadd($priceData['sales']['total']['total_service_fee'], $detail['ticket_customer_service_fee'], 2);
            $priceData['sales']['total']['total_deduction']                   = bcadd($priceData['sales']['total']['total_deduction'], $detail['ticket_customer_deduction_fee'], 2);
            $priceData['sales']['total']['total_total_receivable']            = bcadd($priceData['sales']['total']['total_total_receivable'], $totalReceivable, 2);
            $priceData['sales']['total']['total_refunded_amount']             = bcadd($priceData['sales']['total']['total_refunded_amount'], $customerRefundAmount, 2);
        }

        return $priceData;
    }

    /**
     * 构建订单确认的价格信息
     *
     * @param  int  $orderId  订单ID
     *
     * @return array 格式化的价格信息
     */
    public function buildOrderConfirmPriceData(int $orderId): array
    {
        $orderDetailModel    = model('TicketBookOrderDetailModel');
        $orderPassengerModel = model('TicketBookOrderPassengerModel');

        $orderDetails    = $orderDetailModel->where('order_id', $orderId)->findAll();
        $orderPassengers = $orderPassengerModel->where('order_id', $orderId)->findAll();
        $orderPassengers = array_column($orderPassengers, null, 'id');

        $passengerDetails = [];
        foreach ($orderDetails as $orderDetail) {
            $passengerType                      = $orderPassengers[$orderDetail['order_passenger_id']]['passenger_type'];
            $passengerDetails[$passengerType][] = $orderDetail;
        }

        $priceData = [
            'detail' => [],
            'total'  => [
                'total_customer_price'  => 0.00,
                'total_tax_cn'          => 0.00,
                'total_tax_yq'          => 0.00,
                'total_tax_xt'          => 0.00,
                'total_customer_amount' => 0.00,
            ],
        ];

        foreach ($passengerDetails as $passengerType => $details) {
            $item              = $details[0];
            $passengerTypeText = TicketBookOrderPassengerModel::PASSENGER_TYPE[$passengerType];
            $passengerNum      = count($details);
            $customerPrice     = $item['ticket_marketing_price'] * $passengerNum;
            $taxCn             = $item['ticket_tax_cn'] * $passengerNum;
            $taxYq             = $item['ticket_tax_yq'] * $passengerNum;
            $taxXt             = $item['ticket_tax_xt'] * $passengerNum;
            $customerAmount    = $item['customer_amount'] * $passengerNum;

            $priceData['detail'][] = [
                'name'            => $passengerTypeText . ' * ' . $passengerNum,
                'customer_price'  => $customerPrice,
                'tax_cn'          => $taxCn,
                'tax_yq'          => $taxYq,
                'tax_xt'          => $taxXt,
                'customer_amount' => $customerAmount,
            ];

            $priceData['total']['total_customer_price']  = bcadd($priceData['total']['total_customer_price'], $customerPrice, 2);
            $priceData['total']['total_tax_cn']          = bcadd($priceData['total']['total_tax_cn'], $taxCn, 2);
            $priceData['total']['total_tax_yq']          = bcadd($priceData['total']['total_tax_yq'], $taxYq, 2);
            $priceData['total']['total_tax_xt']          = bcadd($priceData['total']['total_tax_xt'], $taxXt, 2);
            $priceData['total']['total_customer_amount'] = bcadd($priceData['total']['total_customer_amount'], $customerAmount, 2);
        }

        return $priceData;
    }
}
