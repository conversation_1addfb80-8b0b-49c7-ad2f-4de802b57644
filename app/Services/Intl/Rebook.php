<?php

namespace App\Services\Intl;

use App\Models\OrderModel;
use App\Models\PnrPassengerModel;

/**
 * 国际机票改签服务类
 *
 * 提供国际机票改签相关的业务逻辑处理
 */
class Rebook extends IntlBaseService
{
    protected function getOrderModel()
    {
        return model('TicketRebookOrderModel');
    }

    protected function getOrderType(): string
    {
        return 'rebook';
    }

    /**
     * 获取改签申请信息
     *
     * @param  int  $orderId  订单ID
     *
     * @return array
     */
    public function rebookApply(int $orderId): array
    {
        $orderModel = model('TicketBookOrderModel');
        $order      = $orderModel->find($orderId);

        if (empty($order)) {
            throw new \Exception('订单不存在');
        }

        // 构建订单数据
        $orderData = $this->buildOrderData($order, 'rebook');

        // 获取航段信息
        $segmentModel = model('TicketBookOrderSegmentModel');
        $segments     = $segmentModel->where('order_id', $orderId)->findAll();

        $flightData = [];
        if (!empty($segments)) {
            $flightData = $this->buildFlightData($segments, $order['journey_type']);
        }

        // 获取乘客信息
        $passengerData = $this->buildOrderDetailPassengerData($orderId);

        return [
            'order_data'     => $orderData,
            'flight_data'    => $flightData,
            'passenger_data' => $passengerData,
        ];
    }

    /**
     * 优化后的改签航班搜索方法
     * 支持多人多航段的组合情况，按乘客类型分组处理
     *
     * @throws \Exception
     */
    public function getRebookFlight($requestData)
    {
        $orderId        = intval($requestData['order_id']);
        $rebookType     = intval($requestData['rebook_type']);
        $ticketIds      = $requestData['ticket_ids'];
        $flightSegments = $requestData['flight_segments'];

        $orderSegmentModel   = model('TicketBookOrderSegmentModel');
        $orderPassengerModel = model('TicketBookOrderPassengerModel');
        $airportModel        = model('AirportModel');
        $cabinModel          = model('CabinModel');

        // 校验参数有效性
        $orderSegmentIds = array_column($flightSegments, 'segment_id');
        $orderSegments   = $orderSegmentModel->whereIn('id', $orderSegmentIds)->where('order_id', $orderId)->findAll();
        if (count($orderSegments) !== count($orderSegmentIds)) {
            error(0, '航段信息有误');
        }

        // 票号及乘客信息
        $orderPassengers = $orderPassengerModel->whereIn('id', $ticketIds)->findAll();
        if (count($orderPassengers) !== count($ticketIds)) {
            error(0, '票号id信息有误');
        }
        $orderPassengers = array_column($orderPassengers, null, 'id');

        // 按乘客类型分组，每种类型只需要一个代表
        $passengersByType = $this->groupPassengersByType($orderPassengers);

        // 为每种乘客类型和每个航段组合构建API请求参数
        $allResults = [];
        foreach ($passengersByType as $paType => $representativePassenger) {
            $tmpType = PnrPassengerModel::PASSENGER_TYPE_MAP[$paType];
            foreach ($flightSegments as $flightSegment) {
                // 构建单个API请求参数
                $params = $this->buildRebookSearchParams($orderId, $representativePassenger, $flightSegment);

                // 调用API获取改签航班信息
                $api    = new \App\Libraries\Api\IBE\Intl\Rebook();
                $result = $api->rebookSearchFlight($params, $paType);
                if (!empty($result)) {
                    $allResults[] = $result;
                }
            }
        }

        if (empty($allResults)) {
            return [];
        }

        // 处理和格式化最终结果
        return $this->formatRebookSearchResults($allResults, $airportModel, $cabinModel);
    }

    /**
     * 按乘客类型分组，每种类型只取一个代表
     *
     * @param  array  $passengers  乘客列表
     *
     * @return array 按类型分组的乘客代表
     */
    private function groupPassengersByType(array $passengers): array
    {
        $passengersByType = [];

        foreach ($passengers as $passenger) {
            $passengerType = $passenger['passenger_type'];

            // 每种类型只保留第一个乘客作为代表
            if (!isset($passengersByType[$passengerType])) {
                $passengersByType[$passengerType] = $passenger;
            }
        }

        return $passengersByType;
    }

    /**
     * 重新设计的格式化改签搜索结果方法
     * 按航段分组，每个航段包含所有可改签的航班和价差信息
     *
     * @param  array  $allResults  所有搜索结果
     * @param  object  $airportModel  机场模型
     * @param  object  $cabinModel  舱位模型
     *
     * @return array 格式化后的结果
     */
    private function formatRebookSearchResults(array $allResults, $airportModel, $cabinModel): array
    {
        // 获取机场信息
        $airports = $airportModel->select('id,airport_code,city_cn,airport_name_cn')->findAll();
        $airports = array_column($airports, null, 'airport_code');

        // 收集所有航司代码用于获取舱位信息
        $airlineCodes = [];
        foreach ($allResults as $result) {
            $flights = $result['flights'] ?? [];
            foreach ($flights as $flight) {
                $airlineCodes[] = trim($flight['marketing_carrier']);
            }
        }
        $airlineCodes = array_unique($airlineCodes);

        // 获取舱位信息
        $cabins    = $cabinModel->whereIn('airline', $airlineCodes)->findAll();
        $cabinsArr = [];
        foreach ($cabins as $cabin) {
            $tmpKey             = $cabin['airline'] . $cabin['cabin'];
            $cabinsArr[$tmpKey] = $cabin;
        }

        // 按航段分组数据
        $segmentGroups = [];
        foreach ($allResults as $result) {
            $resPassengers   = $result['passengers'] ?? [];
            $flights         = $result['flights'] ?? [];
            $itineraryOffers = $result['itinerary_offers'] ?? [];

            // 按路线分组航班
            foreach ($flights as $flightId => $flight) {
                $route = $flight['departure_airport'] . '-' . $flight['arrival_airport'];

                // 初始化航段分组
                if (!isset($segmentGroups[$route])) {
                    $segmentGroups[$route] = [
                        'departure_airport_city' => $airports[$flight['departure_airport']]['city_cn'] ?? '',
                        'arrival_airport_city'   => $airports[$flight['arrival_airport']]['city_cn'] ?? '',
                        'flights'                => [],
                    ];
                }

                // 查找该航班对应的价差信息
                $pricingInfo = $this->findPricingInfoForFlight($flightId, $itineraryOffers, $resPassengers);
                // 按航班号分组价差信息
                $airlineCode  = trim($flight['marketing_carrier']);
                $flightNumber = $airlineCode . trim($flight['marketing_flight_number']);

                // 检查是否已存在该航班
                $existingFlightIndex = null;
                foreach ($segmentGroups[$route]['flights'] as $index => $existingFlight) {
                    if ($existingFlight['flight_number'] === $flightNumber) {
                        $existingFlightIndex = $index;
                        break;
                    }
                }

                if ($existingFlightIndex !== null) {
                    // 合并价差信息到现有航班
                    $this->mergePricingInfoToFlight($segmentGroups[$route]['flights'][$existingFlightIndex], $pricingInfo, $cabinsArr, $flight);
                } else {
                    // 创建新的航班信息
                    $newFlight = $this->createFlightInfo($flight, $pricingInfo, $airports, $cabinsArr);

                    $segmentGroups[$route]['flights'][] = $newFlight;
                }
            }
        }

        return array_values($segmentGroups);
    }

    /**
     * 创建航班信息
     */
    private function createFlightInfo(array $flight, array $pricingInfo, array $airports, array $cabinsArr): array
    {
        $airlineCode         = trim($flight['marketing_carrier']);
        $flightNumber        = $airlineCode . trim($flight['marketing_flight_number']);
        $formatDepartureTime = date('H:i', strtotime($flight['departure_datetime']));
        $formatArrivalTime   = date('H:i', strtotime($flight['arrival_datetime']));
        $departureDate       = date('Y-m-d', strtotime($flight['departure_datetime']));

        // 构建舱位价格信息
        $cabinsPrice = $this->buildCabinsPricing($flight, $pricingInfo, $cabinsArr, $airlineCode, $flightNumber);

        return [
            'flight_number'          => $flightNumber,
            'meal'                   => '有餐食',
            'air_equip_type'         => $flight['primary_aircraft_type'],
            'departure_airport'      => $flight['departure_airport'],
            'arrival_airport'        => $flight['arrival_airport'],
            'departure_airport_city' => $airports[$flight['departure_airport']]['city_cn'] ?? '',
            'arrival_airport_city'   => $airports[$flight['arrival_airport']]['city_cn'] ?? '',
            'departure_airport_cn'   => $airports[$flight['departure_airport']]['airport_name_cn'] ?? '',
            'arrival_airport_cn'     => $airports[$flight['arrival_airport']]['airport_name_cn'] ?? '',
            'departure_time'         => $formatDepartureTime,
            'arrival_time'           => $formatArrivalTime,
            'departure_date'         => $departureDate,
            'cabins_price'           => $cabinsPrice,
        ];
    }

    /**
     * 构建舱位价格信息
     */
    private function buildCabinsPricing(array $flight, array $pricingInfo, array $cabinsArr, string $airlineCode, string $flightNumber): array
    {
        $cabinNo = trim($flight['cabin_code']);
        $cabin   = $cabinsArr[$airlineCode . $cabinNo] ?? [];

        $economyCabin       = [];
        $businessFirstCabin = [];

        if (!empty($cabin)) {
            // 按乘客类型收集价差信息
            $passengerPricing = [];
            foreach ($pricingInfo as $pricing) {
                $passengerTypeCode                    = $this->getPassengerTypeCodeFromPricing($pricing);
                $passengerPricing[$passengerTypeCode] = [
                    'amount'      => $pricing['reprice_summary']['base_fare']['amount'] ?? 0,
                    'differ_fare' => $pricing['base_fare_diff']['amount'] ?? 0,
                    'reissue_fee' => $pricing['service_fee']['amount'] ?? 0,
                    'taxes'       => $pricing['tax_diff']['amount'] ?? 0,
                ];
            }

            // 构建舱位数据
            $cabinData = [
                'cabin_no'      => $cabinNo,
                'flight_number' => $flightNumber,
                'amount'        => $passengerPricing['ADT']['amount'] ?? 0,
                'differ_fare'   => $passengerPricing['ADT']['differ_fare'] ?? 0,
                'reissue_fee'   => $passengerPricing['ADT']['reissue_fee'] ?? 0,
                'taxes'         => $passengerPricing['ADT']['taxes'] ?? 0,
                'discount'      => $cabin['discount'] . '折',
                'stock'         => $flight['available_seats'] ?? 8,
            ];

            // 如果有儿童或婴儿价差，添加price字段
            if (isset($passengerPricing['CNN']) || isset($passengerPricing['INF'])) {
                $cabinData['price'] = $passengerPricing;
            }

            // 按舱位等级分类
            if ($cabin['cabin_grade_id'] == 1) {
                $economyCabin[] = $cabinData;
            } elseif (in_array($cabin['cabin_grade_id'], [2, 3])) {
                $businessFirstCabin[] = $cabinData;
            }
        }

        // 如果没有舱位信息，创建默认舱位
        if (empty($economyCabin) && empty($businessFirstCabin)) {
            $economyCabin[] = [
                'cabin_no'      => $cabinNo,
                'flight_number' => $flightNumber,
                'amount'        => 0,
                'differ_fare'   => 0,
                'reissue_fee'   => 0,
                'taxes'         => 0,
                'discount'      => '10折',
                'stock'         => $flight['available_seats'] ?? 8,
            ];
        }

        return [
            'economy_cabin'        => $economyCabin,
            'business_first_cabin' => $businessFirstCabin,
        ];
    }

    /**
     * 合并价差信息到现有航班
     */
    private function mergePricingInfoToFlight(array &$existingFlight, array $pricingInfo, array $cabinsArr, array $flight): void
    {
        $airlineCode  = trim($flight['marketing_carrier']);
        $flightNumber = $existingFlight['flight_number'];

        // 更新舱位价格信息
        $newCabinsPrice = $this->buildCabinsPricing($flight, $pricingInfo, $cabinsArr, $airlineCode, $flightNumber);

        // 合并经济舱价格信息
        if (!empty($newCabinsPrice['economy_cabin'])) {
            foreach ($newCabinsPrice['economy_cabin'] as $newCabin) {
                $this->mergeCabinPricing($existingFlight['cabins_price']['economy_cabin'], $newCabin);
            }
        }

        // 合并公务/头等舱价格信息
        if (!empty($newCabinsPrice['business_first_cabin'])) {
            foreach ($newCabinsPrice['business_first_cabin'] as $newCabin) {
                $this->mergeCabinPricing($existingFlight['cabins_price']['business_first_cabin'], $newCabin);
            }
        }
    }

    /**
     * 合并舱位价格信息
     */
    private function mergeCabinPricing(array &$existingCabins, array $newCabin): void
    {
        $cabinNo = $newCabin['cabin_no'];

        // 查找相同舱位
        $existingCabinIndex = null;
        foreach ($existingCabins as $index => $existingCabin) {
            if ($existingCabin['cabin_no'] === $cabinNo) {
                $existingCabinIndex = $index;
                break;
            }
        }

        if ($existingCabinIndex !== null) {
            // 合并price字段
            if (isset($newCabin['price']) && is_array($newCabin['price'])) {
                if (!isset($existingCabins[$existingCabinIndex]['price'])) {
                    $existingCabins[$existingCabinIndex]['price'] = [];
                }
                $existingCabins[$existingCabinIndex]['price'] = array_merge(
                    $existingCabins[$existingCabinIndex]['price'],
                    $newCabin['price']
                );
            } elseif (isset($newCabin['price'])) {
                // 如果现有舱位没有price字段，但新舱位有，则添加
                $existingCabins[$existingCabinIndex]['price'] = $newCabin['price'];
            }

            // 检查是否有儿童或婴儿价差，如果有则确保price字段存在
            if (isset($existingCabins[$existingCabinIndex]['price'])) {
                $priceData = $existingCabins[$existingCabinIndex]['price'];
                if (isset($priceData['CNN']) || isset($priceData['INF'])) {
                    // 更新顶层价格字段为成人价格
                    $adtPrice = $priceData['ADT'] ?? [];
                    if (!empty($adtPrice)) {
                        $existingCabins[$existingCabinIndex]['amount']      = $adtPrice['amount'] ?? 0;
                        $existingCabins[$existingCabinIndex]['differ_fare'] = $adtPrice['differ_fare'] ?? 0;
                        $existingCabins[$existingCabinIndex]['reissue_fee'] = $adtPrice['reissue_fee'] ?? 0;
                        $existingCabins[$existingCabinIndex]['taxes']       = $adtPrice['taxes'] ?? 0;
                    }
                } else {
                    // 如果只有成人价差，移除price字段
                    unset($existingCabins[$existingCabinIndex]['price']);
                }
            }
        } else {
            // 添加新舱位
            $existingCabins[] = $newCabin;
        }
    }

    /**
     * 查找航班对应的价差信息
     *
     * @param  string  $flightId  航班ID
     * @param  array  $itineraryOffers  行程报价信息
     *
     * @return array 价差信息
     */
    private function findPricingInfoForFlight(string $flightId, array $itineraryOffers, array $resPassengers): array
    {
        $pricingInfo = [];

        foreach ($itineraryOffers as $offer) {
            $passengers = $offer['passengers'] ?? [];

            foreach ($passengers as $passenger) {
                $itineraryPricing = $passenger['itinerary_pricing'] ?? [];
                $ticketingInfos   = $itineraryPricing['ticketing_infos'] ?? [];

                foreach ($ticketingInfos as $ticketingInfo) {
                    $coveredFlights = $ticketingInfo['covered_flights'] ?? [];

                    // 检查该航班是否在覆盖的航班列表中
                    if (in_array($flightId, $coveredFlights)) {
                        if (isset($resPassengers[$passenger['passenger_id']])) {
                            $pid         = $resPassengers[$passenger['passenger_id']]['id'];
                            $primaryType = $resPassengers[$passenger['passenger_id']]['primary_type'];
                        } else {
                            $pid         = 0;
                            $primaryType = 'ADT';
                        }

                        $pricingInfo[] = [
                            'passenger_id'      => $pid,
                            'primary_type'      => $primaryType,
                            'base_fare_diff'    => $ticketingInfo['base_fare_diff'],
                            'reprice_fare_diff' => $ticketingInfo['reprice_fare_diff'],
                            'tax_diff'          => $ticketingInfo['tax_diff'],
                            'service_fee'       => $ticketingInfo['service_fee'],
                            'total_fare'        => $ticketingInfo['total_fare'],
                            'reprice_summary'   => $ticketingInfo['reprice_summary'] ?? [],
                        ];
                    }
                }
            }
        }

        return $pricingInfo;
    }

    /**
     * 从价差信息中提取乘客类型代码
     *
     * @param  array  $pricing  价差信息
     *
     * @return string 乘客类型代码 (ADT=成人, CNN=儿童, INF=婴儿)
     */
    private function getPassengerTypeCodeFromPricing(array $pricing): string
    {
        // 直接从primary_type字段获取乘客类型
        $primaryType = $pricing['primary_type'] ?? 'ADT';

        // 标准化乘客类型代码
        switch (strtoupper($primaryType)) {
            case 'CHD':
            case 'CHILD':
            case 'CNN':
                return 'CNN'; // 儿童统一使用CNN
            case 'INF':
            case 'INFANT':
                return 'INF'; // 婴儿
            case 'ADT':
            case 'ADULT':
            default:
                return 'ADT'; // 成人或默认
        }
    }

    /**
     * 构建改签搜索API请求参数
     *
     * @param  int  $orderId  订单ID
     * @param  array  $passenger  乘客信息
     * @param  array  $flightSegment  要改签的航段信息
     * @param  int  $rebookType  改签类型
     *
     * @return array API请求参数
     * @throws \Exception
     */
    private function buildRebookSearchParams(int $orderId, array $passenger, array $flightSegment): array
    {
        $orderSegmentModel = model('TicketBookOrderSegmentModel');
        $flightModel       = model('FlightModel');

        // 获取订单下所有航段信息
        $allOrderSegments = $orderSegmentModel->where('order_id', $orderId)->findAll();

        // 获取所有航班信息
        $flightNumbers = array_column($allOrderSegments, 'flight_number');
        $allFlights    = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flightsMap    = array_column($allFlights, null, 'flight_number');

        // 构建所有航段信息
        $segments           = [];
        $targetSegmentIndex = null;

        foreach ($allOrderSegments as $index => $segment) {
            $flight = $flightsMap[$segment['flight_number']] ?? null;
            if (!$flight) {
                continue; // 跳过没有航班信息的航段
            }

            // 检查是否是要改签的航段
            if ($segment['id'] == $flightSegment['segment_id']) {
                $targetSegmentIndex = $index + 1; // FlightId从1开始
            }

            $segments[] = [
                'marketing_carrier'  => $segment['airline'],
                'flight_no'          => str_replace($segment['airline'], '', $segment['flight_number']),
                'cabin_code'         => $segment['cabin'],
                'departure_datetime' => $segment['departure_datetime'],
                'equipment'          => $flight['air_equip_type'],
                'arrival_datetime'   => $segment['arrival_datetime'] ?? $segment['departure_datetime'], // 如果没有到达时间，使用出发时间
                'departure_city'     => $flight['departure_airport'],
                'arrival_city'       => $flight['arrival_airport'],
                'booking_status'     => $segment['action_code'],
            ];
        }

        // 获取要改签航段的航班信息
        $targetSegment = null;
        $targetFlight  = null;
        foreach ($allOrderSegments as $segment) {
            if ($segment['id'] == $flightSegment['segment_id']) {
                $targetSegment = $segment;
                $targetFlight  = $flightsMap[$segment['flight_number']] ?? null;
                break;
            }
        }

        if (!$targetSegment || !$targetFlight) {
            throw new \Exception("找不到要改签的航段信息");
        }

        // 构建新航段信息
        $newSegments = [
            [
                'origin'           => $targetFlight['departure_airport'], // 出发机场不能变
                'destination'      => $targetFlight['arrival_airport'], // 到达机场不能变
                'depart_date'      => $flightSegment['departure_date'], // 新的出发日期
                'covered_segments' => [$targetSegmentIndex], // 对应的原航段FlightId
            ],
        ];

        return [
            // 票号信息
            'ticket_number' => $passenger['ticket_number'],
            'person_name'   => $passenger['person_name'],
            'airline'       => $targetSegment['airline'], // 使用目标航段的航司作为验证承运人

            // 所有航段信息
            'segments'      => $segments,

            // 新航段信息
            'new_segments'  => $newSegments,
        ];
    }

    /**
     * 获取改签订单详情
     * 参照国内改签订单详情接口，保持返回数据结构一致
     *
     * @param  int  $rebookOrderId  改签订单ID
     *
     * @return array
     * @throws \Exception
     */
    public function getRebookDetail(int $rebookOrderId): array
    {
        $rebookOrderModel = model('TicketRebookOrderModel');
        $rebookOrder      = $rebookOrderModel->find($rebookOrderId);

        if (empty($rebookOrder)) {
            throw new \Exception('改签订单不存在');
        }

        // 获取原订单信息
        $originOrderType = $rebookOrder['origin_order_type'];
        $originOrderId   = $rebookOrder['origin_order_id'];

        if ($originOrderType == 1) {
            // 原订单是出票订单
            $orderModel                = model('TicketBookOrderModel');
            $originOrder               = $orderModel->find($originOrderId);
            $originOrderSegmentModel   = model('TicketBookOrderSegmentModel');
            $originOrderPassengerModel = model('TicketBookOrderPassengerModel');
        } else {
            // 原订单是改签订单
            $orderModel                = model('TicketRebookOrderModel');
            $originOrder               = $orderModel->find($originOrderId);
            $originOrderSegmentModel   = model('TicketRebookOrderSegmentModel');
            $originOrderPassengerModel = model('TicketRebookOrderPassengerModel');
        }

        if (empty($originOrder)) {
            throw new \Exception('原订单不存在');
        }

        // 构建改签订单基础信息
        $orderData = $this->buildRebookOrderData($rebookOrder);

        // 获取原航班信息
        $originOrderSegments = $originOrderSegmentModel->where('order_id', $originOrderId)->findAll();
        $originFlightData    = $this->buildFlightData($originOrderSegments, $originOrder['journey_type']);

        // 获取新航班信息
        $rebookOrderSegmentModel = model('TicketRebookOrderSegmentModel');
        $rebookOrderSegments     = $rebookOrderSegmentModel->where('order_id', $rebookOrderId)->findAll();
        $newFlightData           = $this->buildFlightData($rebookOrderSegments, $rebookOrder['journey_type'] ?? $originOrder['journey_type']);

        // 获取乘客信息
        $passengerData = $this->buildRebookPassengerData($rebookOrderId);

        // 获取价格信息
        $priceData = $this->buildRebookPriceData($rebookOrderId);

        return [
            'order_data'         => $orderData,
            'origin_flight_data' => $originFlightData,
            'new_flight_data'    => $newFlightData,
            'passenger_data'     => $passengerData,
            'price_data'         => $priceData,
        ];
    }

    /**
     * 计算改签费用
     * 参照国内改签费用接口，保持返回数据结构一致
     *
     * @param  array  $params  改签费用查询参数
     *
     * @return array 改签费用数据
     * @throws \Exception
     */
    public function calculateReissuePrice(array $params): array
    {
        $orderId        = intval($params['order_id']);
        $rebookType     = intval($params['rebook_type']);
        $ticketIds      = $params['ticket_ids'];
        $flightSegments = $params['flight_segments'];

        $orderPassengerModel = model('TicketBookOrderPassengerModel');
        $orderDetailModel    = model('TicketBookOrderDetailModel');
        $orderModel          = model('TicketBookOrderModel');
        $flightModel         = model('FlightModel');

        // 获取订单信息
        $order = $orderModel->find($orderId);
        if (empty($order)) {
            throw new \Exception('订单不存在');
        }

        // 获取乘客信息
        $orderPassengers   = $orderPassengerModel->whereIn('id', $ticketIds)->where('order_id', $orderId)->findAll();
        $orderPassengers   = array_column($orderPassengers, null, 'id');
        $orderPassengerIds = array_column($orderPassengers, 'id');
        $orderDetail       = $orderDetailModel->where('order_id', $orderId)->whereIn('order_passenger_id', $orderPassengerIds)->findAll();

        if (empty($orderPassengers)) {
            throw new \Exception('乘客信息不存在');
        }

        // 获取航班信息
        $flightNumbers = array_column($flightSegments, 'flight_number');
        $flights       = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flights       = array_column($flights, null, 'flight_number');

        // 构建航段参数
        $flightsParams       = [];
        $totalPostChangedFee = 0;
        foreach ($flightSegments as $flightSegment) {
            $flightNumber = $flightSegment['flight_number'];
            $flight       = $flights[$flightNumber] ?? null;
            if (empty($flight)) {
                throw new \Exception("航班 {$flightNumber} 信息不存在");
            }

            // 计算到达日期
            $departureDate = $flightSegment['departure_date'];
            $arrivalDate   = $departureDate;
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrivalDate = date('Y-m-d', strtotime($departureDate . ' +1 day'));
            }
            $departureDateTime = $departureDate . ' ' . $flight['departure_time'] . ':00';
            $arrivalDateTime   = $arrivalDate . ' ' . $flight['arrival_time'] . ':00';

            $flightsParams[]     = [
                'departure_airport' => $flight['departure_airport'],
                'arrival_airport'   => $flight['arrival_airport'],
                'air_equip_type'    => $flight['air_equip_type'],
                'marketing_airline' => $flight['airline_code'],
                'operation_airline' => $flight['airline_code'],
                'flight_number'     => $flight['flight_number'],
                'departure_time'    => $departureDateTime,
                'arrival_time'      => $arrivalDateTime,
                'type'              => 'NORMAL',
                'cabin'             => $flightSegment['cabin_no'],
                'action_code'       => 'HK',
            ];
            $totalPostChangedFee = bcadd($totalPostChangedFee, $flightSegment['changed_fee'], 2);
        }

        // 按乘客类型分组
        $passengerTypeArr = [];
        foreach ($orderPassengers as $passenger) {
            $passengerTypeArr[$passenger['passenger_type']] = [
                'ticket_number'  => $passenger['ticket_number'],
                'is_infant'      => $passenger['passenger_type'] == 3,
                'passenger_name' => $passenger['person_name'],
                'passenger_type' => $passenger['passenger_type'],
            ];
        }

        // 调用IBE接口获取改签费用
        $interfacePrice = [];
        $rebookApi      = new \App\Libraries\Api\IBE\Intl\Rebook();
        foreach ($passengerTypeArr as $passengerType => $value) {
            $apiParams = [
                'ticket_no'        => $value['ticket_number'],
                'exchange_airline' => $flightsParams[0]['marketing_airline'],
                'passenger_name'   => $value['passenger_name'],
                'passenger_id'     => 'P1',
                'new_pnr'          => $order['pnr'],
                'passenger_count'  => 1,
                'segments'         => $flightsParams,
                'involuntary'      => $rebookType == 2,
                'endorsement'      => false,
                'passenger_type'   => PnrPassengerModel::INTL_PASSENGER_MAP[$passengerType],
            ];

            $res = $rebookApi->rebookPrice($apiParams);

            // 解析IBE返回的价格信息
            $changedFee = 0;
            $fareDiff   = 0;
            $taxDiff    = 0;

            if (isset($res['reprice_result']['reprice_fare'])) {
                $repriceFare = $res['reprice_result']['reprice_fare'];
                if (isset($repriceFare['service_fee']['amount'])) {
                    $changedFee = floatval($repriceFare['service_fee']['amount']);
                }
                if (isset($repriceFare['fare_diff']['base_fare_diff']['amount'])) {
                    $fareDiff = floatval($repriceFare['fare_diff']['base_fare_diff']['amount']);
                }
                if (isset($repriceFare['fare_diff']['tax_diff']['amount'])) {
                    $taxDiff = floatval($repriceFare['fare_diff']['tax_diff']['amount']);
                }
            }

            $interfacePrice[$value['passenger_type']] = [
                'changed_fee' => $changedFee,
                'fare_diff'   => $fareDiff,
                'tax_diff'    => $taxDiff,
            ];

            // TODO 和用户提交上来的价格做比较
        }

        // 构建返回数据结构，与国内接口保持一致
        $priceData = [
            'purchase' => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'               => 0.00,
                    'total_tax_cn'                       => 0.00,
                    'total_tax_yq'                       => 0.00,
                    'total_tax_xt'                       => 0.00,
                    'total_fare_diff'                    => 0.00,
                    'total_tax_diff'                     => 0.00,
                    'total_changed_fee'                  => 0.00,
                    'total_service_fee'                  => 0.00,
                    'total_original_supplier_agency_fee' => 0.00,
                    'total_new_supplier_agency_fee'      => 0.00,
                    'total_changed_amount'               => 0.00,
                ],
            ],
            'sales'    => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'               => 0.00,
                    'total_tax_cn'                       => 0.00,
                    'total_tax_yq'                       => 0.00,
                    'total_tax_xt'                       => 0.00,
                    'total_fare_diff'                    => 0.00,
                    'total_tax_diff'                     => 0.00,
                    'total_changed_fee'                  => 0.00,
                    'total_service_fee'                  => 0.00,
                    'total_original_supplier_agency_fee' => '-',
                    'total_new_supplier_agency_fee'      => '-',
                    'total_changed_amount'               => 0.00,
                ],
            ],
        ];

        // 处理每个乘客的详细信息
        foreach ($orderDetail as $key => $val) {
            $orderPassenger = $orderPassengers[$val['order_passenger_id']];
            $passengerType  = $orderPassenger['passenger_type'];

            // 获取接口返回的改签费用
            $priceInfo = $interfacePrice[$passengerType];

            // 计算采购改签总计=票差+税差+改签费+改签服务费-代理费差
            $purchaseChangedAmount = 0;
            $purchaseChangedAmount = bcadd($purchaseChangedAmount, $priceInfo['fare_diff'], 2);
            $purchaseChangedAmount = bcadd($purchaseChangedAmount, $priceInfo['tax_diff'], 2);
            $purchaseChangedAmount = bcadd($purchaseChangedAmount, $priceInfo['changed_fee'], 2);
            $purchaseChangedAmount = bcadd($purchaseChangedAmount, 0, 2); // 服务费
            // 代理费差
            $agencyFeeDiff         = bcsub(0, 0, 2);
            $purchaseChangedAmount = bcsub($purchaseChangedAmount, $agencyFeeDiff, 2);

            // 采购详情
            $priceData['purchase']['detail'][] = [
                'order_id'                     => $orderPassenger['order_id'],
                'ticket_id'                    => $orderPassenger['id'],
                'ticket_number'                => $orderPassenger['ticket_number'],
                'person_name'                  => $orderPassenger['person_name'],
                'marketing_price'              => $val['ticket_marketing_price'],
                'tax_cn'                       => $val['ticket_tax_cn'],
                'tax_yq'                       => $val['ticket_tax_yq'],
                'tax_xt'                       => $val['ticket_tax_xt'],
                'fare_diff'                    => $priceInfo['fare_diff'],
                'tax_diff'                     => $priceInfo['tax_diff'],
                'changed_fee'                  => $priceInfo['changed_fee'],
                'service_fee'                  => '0.00',
                'original_supplier_agency_fee' => '0.00',
                'new_supplier_agency_fee'      => '0.00',
                'changed_amount'               => $purchaseChangedAmount,
            ];

            // 更新采购总计
            $priceData['purchase']['total']['total_customer_price']               = bcadd($priceData['purchase']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);
            $priceData['purchase']['total']['total_tax_cn']                       = bcadd($priceData['purchase']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);
            $priceData['purchase']['total']['total_tax_yq']                       = bcadd($priceData['purchase']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);
            $priceData['purchase']['total']['total_tax_xt']                       = bcadd($priceData['purchase']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);
            $priceData['purchase']['total']['total_fare_diff']                    = bcadd($priceData['purchase']['total']['total_fare_diff'], $priceInfo['fare_diff'], 2);
            $priceData['purchase']['total']['total_tax_diff']                     = bcadd($priceData['purchase']['total']['total_tax_diff'], $priceInfo['tax_diff'], 2);
            $priceData['purchase']['total']['total_changed_fee']                  = bcadd($priceData['purchase']['total']['total_changed_fee'], $priceInfo['changed_fee'], 2);
            $priceData['purchase']['total']['total_service_fee']                  = bcadd($priceData['purchase']['total']['total_service_fee'], 0, 2);
            $priceData['purchase']['total']['total_original_supplier_agency_fee'] = bcadd($priceData['purchase']['total']['total_original_supplier_agency_fee'], 0, 2);
            $priceData['purchase']['total']['total_new_supplier_agency_fee']      = bcadd($priceData['purchase']['total']['total_new_supplier_agency_fee'], 0, 2);
            $priceData['purchase']['total']['total_changed_amount']               = bcadd($priceData['purchase']['total']['total_changed_amount'], $purchaseChangedAmount, 2);

            // 计算销售改签总计=票差+税差+改签费+改签服务费
            $salesChangedAmount = 0;
            $salesChangedAmount = bcadd($salesChangedAmount, $priceInfo['fare_diff'], 2);
            $salesChangedAmount = bcadd($salesChangedAmount, $priceInfo['tax_diff'], 2);
            $salesChangedAmount = bcadd($salesChangedAmount, $priceInfo['changed_fee'], 2);
            $salesChangedAmount = bcadd($salesChangedAmount, 0, 2); // 服务费

            // 销售详情
            $priceData['sales']['detail'][] = [
                'order_id'                     => $orderPassenger['order_id'],
                'ticket_id'                    => $orderPassenger['id'],
                'ticket_number'                => $orderPassenger['ticket_number'],
                'person_name'                  => $orderPassenger['person_name'],
                'marketing_price'              => $val['ticket_marketing_price'],
                'tax_cn'                       => $val['ticket_tax_cn'],
                'tax_yq'                       => $val['ticket_tax_yq'],
                'tax_xt'                       => $val['ticket_tax_xt'],
                'fare_diff'                    => $priceInfo['fare_diff'],
                'tax_diff'                     => $priceInfo['tax_diff'],
                'changed_fee'                  => $priceInfo['changed_fee'],
                'service_fee'                  => '0.00',
                'original_supplier_agency_fee' => '-',
                'new_supplier_agency_fee'      => '-',
                'changed_amount'               => $salesChangedAmount,
            ];

            // 更新销售总计
            $priceData['sales']['total']['total_customer_price']               = bcadd($priceData['sales']['total']['total_customer_price'], $val['ticket_marketing_price'], 2);
            $priceData['sales']['total']['total_tax_cn']                       = bcadd($priceData['sales']['total']['total_tax_cn'], $val['ticket_tax_cn'], 2);
            $priceData['sales']['total']['total_tax_yq']                       = bcadd($priceData['sales']['total']['total_tax_yq'], $val['ticket_tax_yq'], 2);
            $priceData['sales']['total']['total_tax_xt']                       = bcadd($priceData['sales']['total']['total_tax_xt'], $val['ticket_tax_xt'], 2);
            $priceData['sales']['total']['total_fare_diff']                    = bcadd($priceData['sales']['total']['total_fare_diff'], $priceInfo['fare_diff'], 2);
            $priceData['sales']['total']['total_tax_diff']                     = bcadd($priceData['sales']['total']['total_tax_diff'], $priceInfo['tax_diff'], 2);
            $priceData['sales']['total']['total_changed_fee']                  = bcadd($priceData['sales']['total']['total_changed_fee'], $priceInfo['changed_fee'], 2);
            $priceData['sales']['total']['total_service_fee']                  = bcadd($priceData['sales']['total']['total_service_fee'], 0, 2);
            $priceData['sales']['total']['total_original_supplier_agency_fee'] = '-';
            $priceData['sales']['total']['total_new_supplier_agency_fee']      = '-';
            $priceData['sales']['total']['total_changed_amount']               = bcadd($priceData['sales']['total']['total_changed_amount'], $salesChangedAmount, 2);
        }

        return $priceData;
    }

    /**
     * 确认改签
     * 参照国内改签确认接口，保持业务逻辑一致
     *
     * @param  array  $params  改签确认参数
     *
     * @return int 改签订单ID
     * @throws \Exception
     */
    public function confirmRebook(array $params): int
    {
        $orderId          = intval($params['order_id']);
        $rebookType       = intval($params['rebook_type']);
        $orderType        = intval($params['order_type']);
        $rebookPurpose    = intval($params['rebook_purpose']);
        $isSendSms        = intval($params['is_send_sms']);
        $contactName      = trim($params['contact_name']);
        $contactEmail     = trim($params['contact_email']);
        $contactTelephone = trim($params['contact_telephone']);
        $remark           = trim($params['remark']);

        $postTickets = $params['tickets'];
        $postTickets = array_column($postTickets, null, 'ticket_id');
        $ticketIds   = array_column($postTickets, 'ticket_id');

        $postOldFlights = $params['old_flight_segments'];
        $postNewFlights = $params['new_flight_segments'];

        if (count($postOldFlights) != count($postNewFlights)) {
            throw new \Exception('新旧航段数量不匹配');
        }

        // 获取订单信息
        $orderModel                       = model('TicketBookOrderModel');
        $rebookOrderModel                 = model('TicketRebookOrderModel');
        $rebookOrderPassengerSegmentModel = model('TicketRebookOrderPassengerSegmentModel');
        $orderPassengerModel              = model('TicketBookOrderPassengerModel');
        $rebookOrderPassengerModel        = model('TicketRebookOrderPassengerModel');
        $flightModel                      = model('FlightModel');
        $orderDetailModel                 = model('TicketRebookOrderDetailModel');
        $rebookOrderSegmentModel          = model('TicketRebookOrderSegmentModel');
        $bookOrderSegmentModel            = model('TicketBookOrderSegmentModel');
        $bookOrderPassengerSegmentModel   = model('TicketBookOrderPassengerSegmentModel');

        if ($orderType == 1) {
            $order = $orderModel->find($orderId);
        } else {
            $order = $rebookOrderModel->find($orderId);
        }

        if (empty($order)) {
            throw new \Exception('订单不存在');
        }

        // 获取乘客信息
        if ($orderType == 1) {
            $orderPassengers = $orderPassengerModel->whereIn('id', $ticketIds)->findAll();
        } else {
            $orderPassengers = $rebookOrderPassengerModel->whereIn('id', $ticketIds)->findAll();
        }

        if (empty($orderPassengers)) {
            throw new \Exception('乘客信息不存在');
        }

        $orderPassengers = array_column($orderPassengers, null, 'ticket_number');

        // 获取航班信息
        $flightNumbers = array_column($postNewFlights, 'flight_number');
        $flights       = $flightModel->whereIn('flight_number', $flightNumbers)->findAll();
        $flights       = array_column($flights, null, 'flight_number');

        // 构建IBE接口参数
        $requestTickets       = [];
        $requestNewFlights    = [];
        $totalMarketingAmount = 0;

        // 构建票号信息
        foreach ($postTickets as $ticketInfo) {
            $ticketId       = $ticketInfo['ticket_id'];
            $orderPassenger = null;

            // 查找对应的乘客信息
            foreach ($orderPassengers as $passenger) {
                if ($passenger['id'] == $ticketId) {
                    $orderPassenger = $passenger;
                    break;
                }
            }

            if (empty($orderPassenger)) {
                throw new \Exception("票号ID {$ticketId} 对应的乘客信息不存在");
            }

            $requestTickets[] = [
                'ticket_number_before'   => $orderPassenger['ticket_number'],
                'is_infant'              => $orderPassenger['passenger_type'] == 3,
                'passenger_name'         => $orderPassenger['person_name'],
                'involuntary_identifier' => $rebookType == 2,
                'airline'                => substr($orderPassenger['ticket_number'], 0, 3),
                'ei'                     => '不得退改签',
                'exchange_type'          => 1,
                'exchange_fee'           => $ticketInfo['changed_fee'],
                'change_fee'             => $ticketInfo['changed_fee'],
                'ticket_fare'            => $ticketInfo['marketing_price'],
                'fare_diff'              => $ticketInfo['differ_fare'],
                'tax_diff'               => 0,
                'total_fare'             => bcadd($ticketInfo['marketing_price'], $ticketInfo['differ_fare'], 2),
            ];

            $totalMarketingAmount = bcadd($totalMarketingAmount, $ticketInfo['marketing_price'], 2);
        }

        // 构建新航段信息
        foreach ($postNewFlights as $val) {
            $departureDate = $val['departure_date'];
            $flightNumber  = $val['flight_number'];
            $cabinNo       = $val['cabin_no'];
            $flight        = $flights[$flightNumber];

            if (empty($flight)) {
                throw new \Exception("航班 {$flightNumber} 信息不存在");
            }

            $arrivalDate = $departureDate;
            if ($flight['departure_time'] > $flight['arrival_time']) {
                $arrivalDate = date('Y-m-d', strtotime($departureDate . ' +1 day'));
            }

            $requestNewFlights[] = [
                'departure_airport' => $flight['departure_airport'],
                'arrival_airport'   => $flight['arrival_airport'],
                'air_equip_type'    => $flight['air_equip_type'],
                'marketing_airline' => $flight['airline_code'],
                'operation_airline' => $flight['airline_code'],
                'flight_number'     => $flightNumber,
                'departure_time'    => $departureDate . 'T' . $flight['departure_time'] . ':00',
                'arrival_time'      => $arrivalDate . 'T' . $flight['arrival_time'] . ':00',
                'type'              => 'NORMAL',
                'cabin'             => $cabinNo,
                'action_code'       => 'HK',
            ];
        }

        $requestParams = [
            'pnr'     => $order['pnr'],
            'tickets' => $requestTickets,
            'flights' => $requestNewFlights,
        ];

        // 调用IBE改签确认接口
        $rebookApi  = new \App\Libraries\Api\IBE\Intl\Rebook();
        $reissueRes = $rebookApi->rebookConfirm($requestParams);
        if (empty($reissueRes)) {
            throw new \Exception('改签接口请求失败');
        }

        // 记录数据库
        try {
            $db = \Config\Database::connect();
            $db->transStart();

            $oldNewTicketNumberMap = [];
            $journeyInfo           = '';
            $passengerNameArr      = [];

            // 处理IBE返回的票号信息
            foreach ($reissueRes as $val) {
                $ticketNumber  = $val['ticket_number'];
                $passengerName = $val['passenger_name'];
                $flights       = $val['flights'];

                $passengerNameArr[] = $passengerName;

                // 建立新旧票号对应关系
                foreach ($requestTickets as $requestTicket) {
                    if ($requestTicket['passenger_name'] == $passengerName) {
                        $oldNewTicketNumberMap[$requestTicket['ticket_number_before']] = $ticketNumber;
                    }
                }

                foreach ($flights as $flight) {
                    $journeyInfo .= $flight['departure_airport'] . $flight['arrival_airport'];
                }
            }

            // 创建改签订单
            $passengerNames = implode(',', array_column($passengerNameArr, 'surname'));
            $orderNo        = $orderModel->generate_order_no('C');

            $insertOrderId = $rebookOrderModel->insert([
                'order_no'              => $orderNo,
                'origin_order_type'     => $orderType,
                'origin_order_id'       => $order['id'],
                'origin_order_no'       => $order['order_no'],
                'ticket_type'           => 2, // 国际票
                'order_source'          => 1,
                'area_type'             => 2, // 国际
                'customer_type'         => 1,
                'customer_id'           => 0,
                'rebook_type'           => $rebookType,
                'rebook_purpose'        => $rebookPurpose,
                'pnr'                   => $order['pnr'], // 暂时使用原PNR，后续可能需要创建新PNR
                'pnr_id'                => $order['pnr_id'],
                'origin_pnr'            => $order['pnr'],
                'origin_pnr_id'         => $order['pnr_id'],
                'journey_info'          => $journeyInfo,
                'passenger_number'      => count($postTickets),
                'passenger_names'       => $passengerNames,
                'contact_name'          => $contactName,
                'contact_telephone'     => $contactTelephone,
                'contact_email'         => $contactEmail,
                'is_send_sms'           => $isSendSms,
                'remark'                => $remark,
                'total_supplier_amount' => $totalMarketingAmount,
                'total_customer_amount' => $totalMarketingAmount,
                'office'                => config('IBE')->office,
                'status'                => 1,
                'operator_id'           => 1, // TODO: 获取当前用户ID
                'operator_name'         => '测试账号', // TODO: 获取当前用户名
                'created_at'            => time(),
                'updated_at'            => time(),
                'changed_at'            => time(),
            ]);

            // 添加改签订单乘客信息
            $passengerTicketMap = [];
            foreach ($orderPassengers as $val) {
                $oldTicketNumber = $val['ticket_number'];
                $newTicketNumber = $oldNewTicketNumberMap[$oldTicketNumber] ?? $oldTicketNumber;

                $insertOrderPassengerId = $rebookOrderPassengerModel->insert([
                    'order_id'               => $insertOrderId,
                    'passenger_type'         => $val['passenger_type'],
                    'ticket_number'          => $newTicketNumber,
                    'origin_ticket_number'   => $val['ticket_number'],
                    'doc_type'               => $val['doc_type'],
                    'doc_type_detail'        => $val['doc_type_detail'],
                    'doc_id'                 => $val['doc_id'],
                    'person_name'            => $val['person_name'],
                    'name_prn'               => $val['name_prn'],
                    'passenger_age'          => $val['passenger_age'],
                    'language_type'          => $val['language_type'],
                    'doc_issue_country'      => $val['doc_issue_country'],
                    'doc_holder_nationality' => $val['doc_holder_nationality'],
                    'doc_holder_ind'         => $val['doc_holder_ind'],
                    'birthday'               => $val['birthday'],
                    'gender'                 => $val['gender'],
                    'expire_date'            => $val['expire_date'],
                    'given_name'             => $val['given_name'],
                    'surname'                => $val['surname'],
                    'telephone'              => $val['telephone'],
                    'status'                 => 1,
                    'created_at'             => time(),
                    'updated_at'             => time(),
                ]);

                $passengerTicketMap[$newTicketNumber] = $insertOrderPassengerId;

                // 获取对应的票价信息
                $ticketInfo = null;
                foreach ($postTickets as $ticket) {
                    if ($ticket['ticket_id'] == $val['id']) {
                        $ticketInfo = $ticket;
                        break;
                    }
                }

                if (empty($ticketInfo)) {
                    throw new \Exception("票号 {$val['ticket_number']} 对应的价格信息不存在");
                }

                // 计算总采购金额
                $supplierAmount = 0;
                $supplierAmount = bcadd($supplierAmount, $ticketInfo['differ_fare'], 2);
                $supplierAmount = bcadd($supplierAmount, 0, 2); // 税差
                $supplierAmount = bcadd($supplierAmount, $ticketInfo['changed_fee'], 2);

                // 添加订单详情
                $orderDetailModel->insert([
                    'order_id'                    => $insertOrderId,
                    'order_passenger_id'          => $insertOrderPassengerId,
                    'product_type'                => 1,
                    'product_id'                  => 0,
                    'supplier_id'                 => 0,
                    'customer_id'                 => 0,
                    'supplier_amount'             => $supplierAmount,
                    'customer_amount'             => $supplierAmount,
                    'ticket_supplier_price_diff'  => $ticketInfo['differ_fare'],
                    'ticket_supplier_tax_diff'    => 0,
                    'ticket_supplier_change_fee'  => $ticketInfo['changed_fee'],
                    'ticket_supplier_service_fee' => 0,
                    'ticket_supplier_amount'      => $supplierAmount,
                    'ticket_customer_price_diff'  => $ticketInfo['differ_fare'],
                    'ticket_customer_tax_diff'    => 0,
                    'ticket_customer_change_fee'  => $ticketInfo['changed_fee'],
                    'ticket_customer_service_fee' => 0,
                    'ticket_customer_amount'      => $supplierAmount,
                    'origin_price_diff'           => $ticketInfo['differ_fare'],
                    'origin_tax_diff'             => 0,
                    'origin_change_fee'           => $ticketInfo['changed_fee'],
                    'created_at'                  => time(),
                    'updated_at'                  => time(),
                ]);
            }

            // 添加改签订单航段信息
            $addOrderSegments = [];
            $oldFlightNumbers = array_column($postOldFlights, 'flight_number');
            $newFlightNumbers = array_column($postNewFlights, 'flight_number');

            // 查询旧的所有航段信息（不包括要改签的航段）
            if ($orderType == 1) {
                $orderSegments = $bookOrderSegmentModel->where('order_id', $orderId)->findAll();
            } else {
                $orderSegments = $rebookOrderSegmentModel->where('order_id', $orderId)->findAll();
            }

            foreach ($orderSegments as $val) {
                $flightNumber = $val['flight_number'];
                if (in_array($flightNumber, $oldFlightNumbers)) {
                    continue; // 跳过要改签的航段
                }

                $addOrderSegments[] = [
                    'order_id'                => $insertOrderId,
                    'change_type'             => 0, // 未改签
                    'departure_datetime'      => $val['departure_datetime'],
                    'arrival_datetime'        => $val['arrival_datetime'],
                    'code_share_ind'          => $val['code_share_ind'],
                    'airline'                 => $val['airline'],
                    'flight_number'           => $val['flight_number'],
                    'operating_airline'       => $val['operating_airline'],
                    'operating_flight_number' => $val['operating_flight_number'],
                    'cabin'                   => $val['cabin'],
                    'sub_cabin'               => $val['sub_cabin'],
                    'fbc'                     => $val['fbc'],
                    'passenger_number'        => $val['passenger_number'],
                    'action_code'             => $val['action_code'],
                    'created_at'              => time(),
                    'updated_at'              => time(),
                ];
            }
            $flights = $flightModel->whereIn('flight_number', $newFlightNumbers)->findAll();
            $flights = array_column($flights, null, 'flight_number');
            // 添加新的航段信息
            foreach ($postNewFlights as $pf) {
                $flightNumber  = $pf['flight_number'];
                $cabinNo       = $pf['cabin_no'];
                $departureDate = $pf['departure_date'];
                $flight        = $flights[$flightNumber];

                $arrivalDate = $departureDate;
                if ($flight['departure_time'] > $flight['arrival_time']) {
                    $arrivalDate = date('Y-m-d', strtotime($departureDate . ' +1 day'));
                }

                $departureDateTime = $departureDate . ' ' . $flight['departure_time'] . ':00';
                $arrivalDateTime   = $arrivalDate . ' ' . $flight['arrival_time'] . ':00';

                $addOrderSegments[] = [
                    'order_id'                => $insertOrderId,
                    'change_type'             => 1, // 已改签
                    'departure_datetime'      => $departureDateTime,
                    'arrival_datetime'        => $arrivalDateTime,
                    'code_share_ind'          => 0,
                    'airline'                 => $flight['airline_code'],
                    'flight_number'           => $flightNumber,
                    'operating_airline'       => $flight['airline_code'],
                    'operating_flight_number' => $flightNumber,
                    'cabin'                   => $cabinNo,
                    'sub_cabin'               => $cabinNo,
                    'fbc'                     => $orderSegments[0]['fbc'] ?? '',
                    'passenger_number'        => count($ticketIds),
                    'action_code'             => $orderSegments[0]['action_code'] ?? 'HK',
                    'created_at'              => time(),
                    'updated_at'              => time(),
                ];
            }

            if (!empty($addOrderSegments)) {
                $rebookOrderSegmentModel->insertBatch($addOrderSegments);
            }

            // 添加改签订单乘客航段信息
            $rebookOrderPassengerSegmentData = [];
            foreach ($reissueRes as $val) {
                $ticketNumber = $val['ticket_number'];
                $flights      = $val['flights'];

                foreach ($flights as $flight) {
                    $rebookOrderPassengerSegmentData[] = [
                        'passenger_id'       => $passengerTicketMap[$ticketNumber] ?? '',
                        'ticket_number'      => $ticketNumber,
                        'flight_number'      => $flight['flight_number'],
                        'departure_datetime' => date('Y-m-d H:i:s', strtotime($flight['departure_time'])),
                        'departure_airport'  => $flight['departure_airport'],
                        'arrival_airport'    => $flight['arrival_airport'],
                        'marketing_airline'  => $flight['airline_code'],
                        'ticket_status'      => 'OPEN FOR USE',
                        'status'             => 1,
                        'created_at'         => time(),
                        'updated_at'         => time(),
                    ];
                }
            }
            if (!empty($rebookOrderPassengerSegmentData)) {
                $rebookOrderPassengerSegmentModel->insertBatch($rebookOrderPassengerSegmentData);
            }

            // 更新旧票号对应航班的状态为已改签
            foreach ($ticketIds as $ticketId) {
                foreach ($oldFlightNumbers as $flightNumber) {
                    if ($orderType == 1) {
                        $bookOrderPassengerSegmentModel->where([
                            'passenger_id'  => $ticketId,
                            'flight_number' => $flightNumber,
                        ])->set([
                            'ticket_status' => 'EXCHANGED',
                            'status'        => 4,
                            'updated_at'    => time(),
                        ])->update();
                    } else {
                        $rebookOrderPassengerSegmentModel->where([
                            'passenger_id'  => $ticketId,
                            'flight_number' => $flightNumber,
                        ])->set([
                            'ticket_status' => 'EXCHANGED',
                            'status'        => 4,
                            'updated_at'    => time(),
                        ])->update();
                    }
                }
            }

            $db->transComplete();

            if ($db->transStatus() === false) {
                throw new \Exception('数据库事务失败');
            }

            return $insertOrderId;

        } catch (\Exception $e) {
            $db->transRollback();
            throw new \Exception('改签确认失败: ' . $e->getMessage());
        }
    }

    /**
     * 构建改签订单基础信息
     *
     * @param  array  $rebookOrder  改签订单数据
     *
     * @return array 格式化的改签订单信息
     */
    private function buildRebookOrderData(array $rebookOrder): array
    {
        $user       = $this->getUser($rebookOrder['operator_id']);
        $department = $this->getDepartment($user['department_id']);

        return [
            'order_no'          => $rebookOrder['order_no'],
            'relate_order_no'   => $rebookOrder['origin_order_no'],
            'pnr'               => $rebookOrder['pnr'],
            'customer_type'     => \App\Models\OrderModel::CUSTOMER_TYPES[$rebookOrder['customer_type']],
            'status'            => $rebookOrder['status'],
            'status_text'       => \App\Models\OrderModel::ORDER_STATUS[$rebookOrder['status']],
            'created_at'        => date('Y-m-d H:i:s', $rebookOrder['created_at']),
            'changed_at'        => date('Y-m-d H:i:s', $rebookOrder['changed_at']),
            'operator_name'     => $department['department_name'] . '|' . $user['name'],
            'rebook_type'       => $rebookOrder['rebook_type'],
            'rebook_type_text'  => OrderModel::REBOOK_TYPE[$rebookOrder['rebook_type']],
            'contact_name'      => $rebookOrder['contact_name'],
            'contact_telephone' => $rebookOrder['contact_telephone'],
            'contact_email'     => $rebookOrder['contact_email'],
            'is_send_sms'       => $rebookOrder['is_send_sms'],
            'remark'            => $rebookOrder['remark'],
        ];
    }

    /**
     * 构建改签订单乘客信息
     *
     * @param  int  $rebookOrderId  改签订单ID
     *
     * @return array 格式化的乘客信息
     */
    private function buildRebookPassengerData(int $rebookOrderId): array
    {
        $rebookOrderPassengerModel        = model('TicketRebookOrderPassengerModel');
        $rebookOrderPassengerSegmentModel = model('TicketRebookOrderPassengerSegmentModel');

        $orderPassengers   = $rebookOrderPassengerModel->where('order_id', $rebookOrderId)->findAll();
        $orderPassengerIds = array_column($orderPassengers, 'id');

        if (empty($orderPassengerIds)) {
            return [];
        }

        // 获取乘客航段信息
        $orderPassengerSegments = $rebookOrderPassengerSegmentModel->whereIn('passenger_id', $orderPassengerIds)->findAll();

        // 按ticket_id分组航段信息
        $passengerSegmentArr = [];
        $allowFlightNumbers  = [];

        foreach ($orderPassengerSegments as $segment) {
            $ticketId                         = $segment['passenger_id'];
            $passengerSegmentArr[$ticketId][] = [
                'ticket_id'     => $ticketId,
                'flight_number' => $segment['flight_number'],
                'status'        => $segment['status'],
                'status_text'   => \App\Models\PnrTicketSegmentModel::STATUS[$segment['status']],
            ];

            if ($segment['status'] == 1) {
                $allowFlightNumbers[$ticketId][] = $segment['flight_number'];
            }
        }

        $passengerData = [];
        foreach ($orderPassengers as $passenger) {
            $ticketId     = $passenger['id'];
            $ticketNumber = $passenger['ticket_number'];

            // 计算退改状态
            $refundChangeStatus = $this->calculateRefundChangeStatus($passengerSegmentArr[$ticketId] ?? []);

            $passengerData[] = [
                'rph'                  => $passenger['rph'] ?? 1,
                'ticket_id'            => $ticketId,
                'pnr_passenger_id'     => $passenger['id'],
                'order_id'             => $rebookOrderId,
                'person_name'          => $passenger['person_name'],
                'gender'               => \App\Models\PnrPassengerModel::GENDER[$passenger['gender']],
                'passenger_type'       => $passenger['passenger_type'],
                'passenger_type_text'  => \App\Models\PnrPassengerModel::PASSENGER_TYPE[$passenger['passenger_type']],
                'doc_type'             => $passenger['doc_type'],
                'doc_type_text'        => \App\Models\PnrPassengerModel::DOC_TYPE[$passenger['doc_type']],
                'doc_id'               => \App\Helpers\Tools\Idcard::mask($passenger['doc_id']),
                'telephone'            => \App\Helpers\Tools\Char::mask_phone($passenger['telephone']),
                'ticket_number'        => \App\Helpers\Tools\Char::mask_ticket($ticketNumber),
                'refund_change_status' => $refundChangeStatus,
                'ticket_segments'      => $passengerSegmentArr[$ticketId] ?? [],
                'allow_flight_numbers' => $allowFlightNumbers[$ticketId] ?? [],
            ];
        }

        return $passengerData;
    }

    /**
     * 计算退改状态
     *
     * @param  array  $ticketSegments  票号对应的航段数组
     *
     * @return string 退改状态
     */
    private function calculateRefundChangeStatus(array $ticketSegments): string
    {
        if (empty($ticketSegments)) {
            return '不可退';
        }

        $availableQty = 0; // 可退票数量
        $refundedQty  = 0;  // 已退票数

        foreach ($ticketSegments as $segment) {
            if ($segment['status'] == 1) {
                $availableQty++;
            } elseif ($segment['status'] == 5) {
                $refundedQty++;
            }
        }

        if ($availableQty >= 1) {
            return '可退票';
        } elseif ($refundedQty == count($ticketSegments)) {
            return '已退票';
        } else {
            return '不可退';
        }
    }

    /**
     * 构建改签订单价格信息
     *
     * @param  int  $rebookOrderId  改签订单ID
     *
     * @return array 格式化的价格信息
     */
    private function buildRebookPriceData(int $rebookOrderId): array
    {
        $rebookOrderDetailModel    = model('TicketRebookOrderDetailModel');
        $rebookOrderPassengerModel = model('TicketRebookOrderPassengerModel');

        $orderDetail     = $rebookOrderDetailModel->where('order_id', $rebookOrderId)->findAll();
        $orderPassengers = $rebookOrderPassengerModel->where('order_id', $rebookOrderId)->findAll();
        $orderPassengers = array_column($orderPassengers, null, 'id');

        $priceData = [
            'purchase' => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'               => 0.00,
                    'total_tax_cn'                       => 0.00,
                    'total_tax_yq'                       => 0.00,
                    'total_tax_xt'                       => 0.00,
                    'total_fare_diff'                    => 0.00,
                    'total_tax_diff'                     => 0.00,
                    'total_changed_fee'                  => 0.00,
                    'total_service_fee'                  => 0.00,
                    'total_original_supplier_agency_fee' => 0.00,
                    'total_new_supplier_agency_fee'      => 0.00,
                    'total_changed_amount'               => 0.00,
                ],
            ],
            'sales'    => [
                'detail' => [],
                'total'  => [
                    'total_customer_price'               => 0.00,
                    'total_tax_cn'                       => 0.00,
                    'total_tax_yq'                       => 0.00,
                    'total_tax_xt'                       => 0.00,
                    'total_fare_diff'                    => 0.00,
                    'total_tax_diff'                     => 0.00,
                    'total_changed_fee'                  => 0.00,
                    'total_service_fee'                  => 0.00,
                    'total_original_supplier_agency_fee' => '-',
                    'total_new_supplier_agency_fee'      => '-',
                    'total_changed_amount'               => 0.00,
                ],
            ],
        ];

        foreach ($orderDetail as $detail) {
            $orderPassenger = $orderPassengers[$detail['order_passenger_id']];

            // 采购详情
            $priceData['purchase']['detail'][] = [
                'order_id'                     => $orderPassenger['order_id'],
                'ticket_id'                    => $orderPassenger['id'],
                'ticket_number'                => $orderPassenger['ticket_number'],
                'person_name'                  => $orderPassenger['person_name'],
                'marketing_price'              => 0, // 改签订单没有原始票面价
                'tax_cn'                       => 0,
                'tax_yq'                       => 0,
                'tax_xt'                       => 0,
                'fare_diff'                    => $detail['ticket_supplier_price_diff'],
                'tax_diff'                     => $detail['ticket_supplier_tax_diff'],
                'changed_fee'                  => $detail['ticket_supplier_change_fee'],
                'service_fee'                  => $detail['ticket_supplier_service_fee'],
                'original_supplier_agency_fee' => 0,
                'new_supplier_agency_fee'      => 0,
                'changed_amount'               => $detail['ticket_supplier_amount'],
            ];

            // 销售详情
            $priceData['sales']['detail'][] = [
                'order_id'                     => $orderPassenger['order_id'],
                'ticket_id'                    => $orderPassenger['id'],
                'ticket_number'                => $orderPassenger['ticket_number'],
                'person_name'                  => $orderPassenger['person_name'],
                'marketing_price'              => 0,
                'tax_cn'                       => 0,
                'tax_yq'                       => 0,
                'tax_xt'                       => 0,
                'fare_diff'                    => $detail['ticket_customer_price_diff'],
                'tax_diff'                     => $detail['ticket_customer_tax_diff'],
                'changed_fee'                  => $detail['ticket_customer_change_fee'],
                'service_fee'                  => $detail['ticket_customer_service_fee'],
                'original_supplier_agency_fee' => '-',
                'new_supplier_agency_fee'      => '-',
                'changed_amount'               => $detail['ticket_customer_amount'],
            ];

            // 更新采购总计
            $priceData['purchase']['total']['total_fare_diff']      = bcadd($priceData['purchase']['total']['total_fare_diff'], $detail['ticket_supplier_price_diff'], 2);
            $priceData['purchase']['total']['total_tax_diff']       = bcadd($priceData['purchase']['total']['total_tax_diff'], $detail['ticket_supplier_tax_diff'], 2);
            $priceData['purchase']['total']['total_changed_fee']    = bcadd($priceData['purchase']['total']['total_changed_fee'], $detail['ticket_supplier_change_fee'], 2);
            $priceData['purchase']['total']['total_service_fee']    = bcadd($priceData['purchase']['total']['total_service_fee'], $detail['ticket_supplier_service_fee'], 2);
            $priceData['purchase']['total']['total_changed_amount'] = bcadd($priceData['purchase']['total']['total_changed_amount'], $detail['ticket_supplier_amount'], 2);

            // 更新销售总计
            $priceData['sales']['total']['total_fare_diff']      = bcadd($priceData['sales']['total']['total_fare_diff'], $detail['ticket_customer_price_diff'], 2);
            $priceData['sales']['total']['total_tax_diff']       = bcadd($priceData['sales']['total']['total_tax_diff'], $detail['ticket_customer_tax_diff'], 2);
            $priceData['sales']['total']['total_changed_fee']    = bcadd($priceData['sales']['total']['total_changed_fee'], $detail['ticket_customer_change_fee'], 2);
            $priceData['sales']['total']['total_service_fee']    = bcadd($priceData['sales']['total']['total_service_fee'], $detail['ticket_customer_service_fee'], 2);
            $priceData['sales']['total']['total_changed_amount'] = bcadd($priceData['sales']['total']['total_changed_amount'], $detail['ticket_customer_amount'], 2);
        }

        return $priceData;
    }
}