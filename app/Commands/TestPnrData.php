<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Models\TicketBookOrderModel;

class TestPnrData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'test:pnr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '测试PNR数据是否正确保存';

    /**
     * Actually execute a command.
     *
     * @param array $params
     */
    public function run(array $params)
    {
        CLI::write('检查PNR数据...', 'green');
        
        $orderModel = new TicketBookOrderModel();
        
        // 查询最近的订单
        $orders = $orderModel->orderBy('id', 'DESC')->limit(5)->findAll();
        
        if (empty($orders)) {
            CLI::write('没有找到订单数据', 'red');
            return;
        }
        
        CLI::write('最近的订单:', 'yellow');
        foreach ($orders as $order) {
            CLI::write("订单ID: {$order['id']}", 'cyan');
            CLI::write("订单号: {$order['order_no']}", 'cyan');
            CLI::write("PNR: {$order['pnr']}", 'cyan');
            CLI::write("状态: {$order['status']}", 'cyan');
            CLI::write("总金额: {$order['total_customer_amount']}", 'cyan');
            CLI::write("创建时间: " . date('Y-m-d H:i:s', $order['created_at']), 'cyan');
            CLI::write('---', 'white');
        }
    }
}
