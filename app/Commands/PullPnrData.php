<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use App\Services\PnrData\PullPnrDataService;

class PullPnrData extends BaseCommand
{
    /**
     * The Command's Group
     *
     * @var string
     */
    protected $group = 'App';

    /**
     * The Command's Name
     *
     * @var string
     */
    protected $name = 'pull:pnr';

    /**
     * The Command's Description
     *
     * @var string
     */
    protected $description = '从IBE接口拉取PNR数据并存储出票订单';

    /**
     * The Command's Usage
     *
     * @var string
     */
    protected $usage = 'pull:pnr [date]';

    /**
     * The Command's Arguments
     *
     * @var array
     */
    protected $arguments = [
        'date' => '指定拉取数据的日期，格式：YYYY-MM-DD，默认为昨天',
    ];

    /**
     * The Command's Options
     *
     * @var array
     */
    protected $options = [
        '--office'     => '指定OFFICE号，默认使用配置文件中的值',
        '--ticket_num' => '指定电子票号，用于测试单个PNR数据',
        '--pnr'        => '指定PNR号，用于测试单个PNR数据',
    ];

    /**
     * Actually execute a command.
     *
     * @param  array  $params
     */
    public function run(array $params): void
    {
        CLI::write('开始拉取PNR数据...', 'green');

        try {
            // 获取日期参数，默认为昨天
            $date = $params[0] ?? date('Y-m-d', strtotime('-1 day'));

            // 验证日期格式
            if (!$this->validateDate($date)) {
                CLI::error('日期格式错误，请使用 YYYY-MM-DD 格式');

                return;
            }

            // 获取时间范围（前一小时）
            $start = date('H:00:00', strtotime('-1 hour'));
            $end   = date('H:59:59', strtotime('-1 hour'));

            CLI::write("拉取日期: {$date}", 'yellow');

            // 创建服务实例并执行拉取
            $pullService = new PullPnrDataService();
            $result      = $pullService->pullAndSaveData($date, $start, $end);

            // 输出结果
            CLI::write("拉取完成！", 'green');
            CLI::write("总记录数: {$result['total_records']}", 'cyan');
            CLI::write("出票订单数: {$result['ticket_orders']}", 'cyan');
            CLI::write("跳过记录数: {$result['skipped_records']}", 'cyan');

            if (!empty($result['errors'])) {
                CLI::write("错误信息:", 'red');
                foreach ($result['errors'] as $error) {
                    CLI::write("  - {$error}", 'red');
                }
            }

        } catch (\Exception $e) {
            CLI::error('执行失败: ' . $e->getMessage());
            CLI::write($e->getTraceAsString(), 'red');
        }
    }

    /**
     * 验证日期格式
     *
     * @param  string  $date
     *
     * @return bool
     */
    private function validateDate(string $date): bool
    {
        $d = \DateTime::createFromFormat('Y-m-d', $date);

        return $d && $d->format('Y-m-d') === $date;
    }
}
